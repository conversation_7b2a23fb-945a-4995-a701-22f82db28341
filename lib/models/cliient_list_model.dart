// To parse this JSON data, do
//
//     final clientListModel = clientListModelFromJson(jsonString);

import 'dart:convert';

ClientListModel clientListModelFromJson(String str) =>
    ClientListModel.fromJson(json.decode(str));

String clientListModelToJson(ClientListModel data) =>
    json.encode(data.toJson());

class ClientListModel {
  ClientListModel({
    this.list = const [],
    this.page,
    this.size,
    this.totalElements,
  });

  final List<SpotOnClient> list;
  final int? page;
  final int? size;
  final int? totalElements;

  factory ClientListModel.fromJson(Map<String, dynamic> json) =>
      ClientListModel(
        list: json["list"] == null
            ? []
            : List<SpotOnClient>.from(
                json["list"].map((x) => SpotOnClient.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": list == null
            ? null
            : List<dynamic>.from(list.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class SpotOnClient {
  SpotOnClient({
    this.clientId,
    this.clientName,
    this.street,
    this.city,
    this.state,
    this.zip,
    this.country,
    this.contactPerson,
    this.contactEmail,
    this.contactPhone,
    this.remarks,
    this.isActive,
    this.audit,
    this.bol,
    this.dvir,
    this.selected = false,
  });

  final String? clientId;
  final String? clientName;
  final String? street;
  final String? city;
  final String? state;
  final String? zip;
  final String? country;
  final String? contactPerson;
  final String? contactEmail;
  final String? contactPhone;
  final String? remarks;
  final bool? isActive;
  final Audit? audit;
  final bool? bol;
  final bool? dvir;
  bool selected;

  factory SpotOnClient.fromJson(Map<String, dynamic> json) => SpotOnClient(
        clientId: json["clientId"],
        clientName: json["clientName"],
        street: json["street"],
        city: json["city"],
        state: json["state"],
        zip: json["zip"],
        country: json["country"],
        contactPerson: json["contactPerson"],
        contactEmail: json["contactEmail"],
        contactPhone: json["contactPhone"],
        remarks: json["remarks"],
        isActive: json["isActive"],
        bol: json["bol"],
        dvir: json["dvir"],
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
      );

  Map<String, dynamic> toJson() => {
        "clientId": clientId,
        "clientName": clientName,
        "street": street,
        "city": city,
        "state": state,
        "zip": zip,
        "country": country,
        "contactPerson": contactPerson,
        "contactEmail": contactEmail,
        "contactPhone": contactPhone,
        "remarks": remarks,
        "isActive": isActive,
        "bol": bol,
        "dvir": dvir,
        "audit": audit?.toJson(),
      };
}

class Audit {
  Audit({
    this.createdDate,
    this.lastModifiedDate,
    this.createdBy,
    this.lastModifiedBy,
  });

  final String? createdDate;
  final String? lastModifiedDate;
  final EdBy? createdBy;
  final EdBy? lastModifiedBy;

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
        createdBy:
            json["createdBy"] == null ? null : EdBy.fromJson(json["createdBy"]),
        lastModifiedBy: json["lastModifiedBy"] == null
            ? null
            : EdBy.fromJson(json["lastModifiedBy"]),
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
        "createdBy": createdBy?.toJson(),
        "lastModifiedBy":
            lastModifiedBy?.toJson(),
      };
}

class EdBy {
  EdBy({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
  });

  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? timeZone;
  final String? lastLoginTime;

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
      };
}
