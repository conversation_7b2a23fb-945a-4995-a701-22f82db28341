// To parse this JSON data, do
//
//     final errorModel = errorModelFromJson(jsonString);

import 'dart:convert';

ErrorModel errorModelFromJson(String str) =>
    ErrorModel.fromJson(json.decode(str));

String errorModelToJson(ErrorModel data) => json.encode(data.toJson());

class ErrorModel {
  ErrorModel({
    this.time,
    this.status,
    this.errors,
  });

  final DateTime? time;
  final String? status;
  final List<Error>? errors;

  factory ErrorModel.fromJson(Map<String, dynamic> json) => ErrorModel(
        time: json["time"] == null ? null : DateTime.parse(json["time"]),
        status: json["status"],
        errors: json["errors"] == null
            ? null
            : List<Error>.from(json["errors"].map((x) => Error.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "time": time?.toIso8601String(),
        "status": status,
        "errors": errors == null
            ? null
            : List<dynamic>.from(errors!.map((x) => x.toJson())),
      };
}

class Error {
  Error({
    this.code,
    this.message,
  });

  final String? code;
  final String? message;

  factory Error.fromJson(Map<String, dynamic> json) => Error(
        code: json["code"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "code": code,
        "message": message,
      };
}
