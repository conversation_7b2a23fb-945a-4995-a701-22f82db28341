// To parse this JSON data, do
//
//     final spotOnLocationList = spotOnLocationListFromJson(jsonString);

import 'dart:convert';

SpotOnLocationList spotOnLocationListFromJson(String str) =>
    SpotOnLocationList.fromJson(json.decode(str));

String spotOnLocationListTo<PERSON>son(SpotOnLocationList data) =>
    json.encode(data.toJson());

class SpotOnLocationList {
  SpotOnLocationList({
    this.list = const [],
    this.page = 0,
    this.size = 0,
    this.totalElements = 0,
  });

  final List<SpotOnLocation> list;
  final int page;
  final int size;
  final int totalElements;

  factory SpotOnLocationList.fromJson(Map<String, dynamic> json) =>
      SpotOnLocationList(
        list: json["list"] == null
            ? []
            : List<SpotOnLocation>.from(
                json["list"].map((x) => SpotOnLocation.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": list == null
            ? null
            : List<dynamic>.from(list.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class SpotOnLocation {
  SpotOnLocation(
      {this.clientId,
      this.locationId,
      this.locationName,
      this.street,
      this.city,
      this.state,
      this.zip,
      this.country,
      this.latitude,
      this.longitude,
      this.remarks,
      this.isActive,
      this.audit,
      this.trailerStandPhoto});

  final String? clientId;
  String? locationId;
  String? locationName;
  final String? street;
  final String? city;
  final String? state;
  final String? zip;
  final String? country;
  final double? latitude;
  final double? longitude;
  final String? remarks;
  final bool? isActive;
  final Audit? audit;
  final bool? trailerStandPhoto;

  factory SpotOnLocation.fromJson(Map<String, dynamic> json) => SpotOnLocation(
        clientId: json["clientId"],
        locationId: json["locationId"],
        locationName: json["locationName"],
        street: json["street"],
        city: json["city"],
        state: json["state"],
        zip: json["zip"],
        country: json["country"],
        latitude: json["latitude"]?.toDouble(),
        longitude:
            json["longitude"]?.toDouble(),
        remarks: json["remarks"],
        isActive: json["isActive"],
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
        trailerStandPhoto: json["trailerStandPhoto"],
      );

  Map<String, dynamic> toJson() => {
        "clientId": clientId,
        "locationId": locationId,
        "locationName": locationName,
        "street": street,
        "city": city,
        "state": state,
        "zip": zip,
        "country": country,
        "latitude": latitude,
        "longitude": longitude,
        "remarks": remarks,
        "isActive": isActive,
        "audit": audit?.toJson(),
      };
}

class Audit {
  Audit({
    this.createdDate,
    this.lastModifiedDate,
    this.createdBy,
    this.lastModifiedBy,
  });

  final String? createdDate;
  final String? lastModifiedDate;
  final EdBy? createdBy;
  final EdBy? lastModifiedBy;

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
        createdBy:
            json["createdBy"] == null ? null : EdBy.fromJson(json["createdBy"]),
        lastModifiedBy: json["lastModifiedBy"] == null
            ? null
            : EdBy.fromJson(json["lastModifiedBy"]),
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
        "createdBy": createdBy?.toJson(),
        "lastModifiedBy":
            lastModifiedBy?.toJson(),
      };
}

class EdBy {
  EdBy({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
  });

  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? timeZone;
  final String? lastLoginTime;

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
      };
}
