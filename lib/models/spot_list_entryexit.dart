import 'dart:convert';

SpotListeEntryExit spotListeEntryExitFromJson(String str) =>
    SpotListeEntryExit.fromJson(json.decode(str));

String spotListeEntryExitToJson(SpotListeEntryExit data) =>
    json.encode(data.toJson());

class SpotListeEntryExit {
  final List<SpotListElement>? list;
  final int? page;
  final int? size;
  final int? totalElements;

  SpotListeEntryExit({
    this.list,
    this.page,
    this.size,
    this.totalElements,
  });

  SpotListeEntryExit copyWith({
    List<SpotListElement>? list,
    int? page,
    int? size,
    int? totalElements,
  }) =>
      SpotListeEntryExit(
        list: list ?? this.list,
        page: page ?? this.page,
        size: size ?? this.size,
        totalElements: totalElements ?? this.totalElements,
      );

  factory SpotListeEntryExit.fromJson(Map<String, dynamic> json) =>
      SpotListeEntryExit(
        list: json["list"] == null
            ? []
            : List<SpotListElement>.from(
                json["list"]!.map((x) => SpotListElement.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": list == null
            ? []
            : List<dynamic>.from(list!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class SpotListElement {
  final String? locationId;
  final String? spotId;
  final String? spotName;
  final String? type;
  final String? status;
  final num? latitude; // Changed from int? to num?
  final num? longitude; // Changed from int? to num?
  final String? remarks;
  final bool? isActive;
  final dynamic emptiedSinceSeconds;
  final dynamic occupiedSinceSeconds;
  final Fleet? fleet;
  final String? locationName;
  final DateTime? lastOccupiedTime;
  final DateTime? lastEmptiedTime;
  final bool? isOccupied;
  final Audit? audit;

  SpotListElement({
    this.locationId,
    this.spotId,
    this.spotName,
    this.type,
    this.status,
    this.latitude,
    this.longitude,
    this.remarks,
    this.isActive,
    this.emptiedSinceSeconds,
    this.occupiedSinceSeconds,
    this.fleet,
    this.locationName,
    this.lastOccupiedTime,
    this.lastEmptiedTime,
    this.isOccupied,
    this.audit,
  });

  SpotListElement copyWith({
    String? locationId,
    String? spotId,
    String? spotName,
    String? type,
    String? status,
    num? latitude, // Changed from int? to num?
    num? longitude, // Changed from int? to num?
    String? remarks,
    bool? isActive,
    dynamic emptiedSinceSeconds,
    dynamic occupiedSinceSeconds,
    Fleet? fleet,
    String? locationName,
    DateTime? lastOccupiedTime,
    DateTime? lastEmptiedTime,
    bool? isOccupied,
    Audit? audit,
  }) =>
      SpotListElement(
        locationId: locationId ?? this.locationId,
        spotId: spotId ?? this.spotId,
        spotName: spotName ?? this.spotName,
        type: type ?? this.type,
        status: status ?? this.status,
        latitude: latitude ?? this.latitude,
        longitude: longitude ?? this.longitude,
        remarks: remarks ?? this.remarks,
        isActive: isActive ?? this.isActive,
        emptiedSinceSeconds: emptiedSinceSeconds ?? this.emptiedSinceSeconds,
        occupiedSinceSeconds: occupiedSinceSeconds ?? this.occupiedSinceSeconds,
        fleet: fleet ?? this.fleet,
        locationName: locationName ?? this.locationName,
        lastOccupiedTime: lastOccupiedTime ?? this.lastOccupiedTime,
        lastEmptiedTime: lastEmptiedTime ?? this.lastEmptiedTime,
        isOccupied: isOccupied ?? this.isOccupied,
        audit: audit ?? this.audit,
      );

  factory SpotListElement.fromJson(Map<String, dynamic> json) =>
      SpotListElement(
        locationId: json["locationId"],
        spotId: json["spotId"],
        spotName: json["spotName"],
        type: json["type"],
        status: json["status"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        remarks: json["remarks"],
        isActive: json["isActive"],
        emptiedSinceSeconds: json["emptiedSinceSeconds"],
        occupiedSinceSeconds: json["occupiedSinceSeconds"],
        fleet: json["fleet"] == null ? null : Fleet.fromJson(json["fleet"]),
        locationName: json["locationName"],
        lastOccupiedTime: json["lastOccupiedTime"] == null
            ? null
            : DateTime.parse(json["lastOccupiedTime"]),
        lastEmptiedTime: json["lastEmptiedTime"] == null
            ? null
            : DateTime.parse(json["lastEmptiedTime"]),
        isOccupied: json["isOccupied"],
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
      );

  Map<String, dynamic> toJson() => {
        "locationId": locationId,
        "spotId": spotId,
        "spotName": spotName,
        "type": type,
        "status": status,
        "latitude": latitude,
        "longitude": longitude,
        "remarks": remarks,
        "isActive": isActive,
        "emptiedSinceSeconds": emptiedSinceSeconds,
        "occupiedSinceSeconds": occupiedSinceSeconds,
        "fleet": fleet?.toJson(),
        "locationName": locationName,
        "lastOccupiedTime": lastOccupiedTime?.toIso8601String(),
        "lastEmptiedTime": lastEmptiedTime?.toIso8601String(),
        "isOccupied": isOccupied,
        "audit": audit?.toJson(),
      };
}

class Audit {
  final String? createdDate;
  final String? lastModifiedDate;
  final EdBy? createdBy;
  final EdBy? lastModifiedBy;

  Audit({
    this.createdDate,
    this.lastModifiedDate,
    this.createdBy,
    this.lastModifiedBy,
  });

  Audit copyWith({
    String? createdDate,
    String? lastModifiedDate,
    EdBy? createdBy,
    EdBy? lastModifiedBy,
  }) =>
      Audit(
        createdDate: createdDate ?? this.createdDate,
        lastModifiedDate: lastModifiedDate ?? this.lastModifiedDate,
        createdBy: createdBy ?? this.createdBy,
        lastModifiedBy: lastModifiedBy ?? this.lastModifiedBy,
      );

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
        createdBy:
            json["createdBy"] == null ? null : EdBy.fromJson(json["createdBy"]),
        lastModifiedBy: json["lastModifiedBy"] == null
            ? null
            : EdBy.fromJson(json["lastModifiedBy"]),
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
        "createdBy": createdBy?.toJson(),
        "lastModifiedBy": lastModifiedBy?.toJson(),
      };
}

class EdBy {
  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? timeZone;
  final String? lastLoginTime;
  final bool? isActive;
  final bool? isAdUser;
  final DateTime? idleSince;

  EdBy({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
    this.isActive,
    this.isAdUser,
    this.idleSince,
  });

  EdBy copyWith({
    String? userId,
    String? firstName,
    String? lastName,
    String? email,
    String? phone,
    String? timeZone,
    String? lastLoginTime,
    bool? isActive,
    bool? isAdUser,
    DateTime? idleSince,
  }) =>
      EdBy(
        userId: userId ?? this.userId,
        firstName: firstName ?? this.firstName,
        lastName: lastName ?? this.lastName,
        email: email ?? this.email,
        phone: phone ?? this.phone,
        timeZone: timeZone ?? this.timeZone,
        lastLoginTime: lastLoginTime ?? this.lastLoginTime,
        isActive: isActive ?? this.isActive,
        isAdUser: isAdUser ?? this.isAdUser,
        idleSince: idleSince ?? this.idleSince,
      );

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
        isActive: json["isActive"],
        isAdUser: json["isADUser"],
        idleSince: json["idleSince"] == null
            ? null
            : DateTime.parse(json["idleSince"]),
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
        "isActive": isActive,
        "isADUser": isAdUser,
        "idleSince": idleSince?.toIso8601String(),
      };
}

class Fleet {
  final String? fleetId;
  final String? carrier;
  final String? type;
  final String? unitNumber;
  final String? remarks;
  final bool? isActive;
  final String? fleetStatus;
  final String? owner;

  Fleet({
    this.fleetId,
    this.carrier,
    this.type,
    this.unitNumber,
    this.remarks,
    this.isActive,
    this.fleetStatus,
    this.owner,
  });

  Fleet copyWith({
    String? fleetId,
    String? carrier,
    String? type,
    String? unitNumber,
    String? remarks,
    bool? isActive,
    String? fleetStatus,
    String? owner,
  }) =>
      Fleet(
        fleetId: fleetId ?? this.fleetId,
        carrier: carrier ?? this.carrier,
        type: type ?? this.type,
        unitNumber: unitNumber ?? this.unitNumber,
        remarks: remarks ?? this.remarks,
        isActive: isActive ?? this.isActive,
        fleetStatus: fleetStatus ?? this.fleetStatus,
        owner: owner ?? this.owner,
      );

  factory Fleet.fromJson(Map<String, dynamic> json) => Fleet(
        fleetId: json["fleetId"],
        carrier: json["carrier"],
        type: json["type"],
        unitNumber: json["unitNumber"],
        remarks: json["remarks"],
        isActive: json["isActive"],
        fleetStatus: json["fleetStatus"],
        owner: json["owner"],
      );

  Map<String, dynamic> toJson() => {
        "fleetId": fleetId,
        "carrier": carrier,
        "type": type,
        "unitNumber": unitNumber,
        "remarks": remarks,
        "isActive": isActive,
        "fleetStatus": fleetStatus,
        "owner": owner,
      };
}

//mmmmmmmm
