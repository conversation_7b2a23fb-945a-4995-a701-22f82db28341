// To parse this JSON data, do
//
//     final clientDetailsNewModel = clientDetailsNewModelFromJson(jsonString);

import 'dart:convert';

ClientDetailsNewModel clientDetailsNewModelFromJson(String str) =>
    ClientDetailsNewModel.fromJson(json.decode(str));

String clientDetailsNewModelToJson(ClientDetailsNewModel data) =>
    json.encode(data.toJson());

class ClientDetailsNewModel {
  String? clientId;
  String? clientName;
  String? street;
  String? city;
  String? state;
  String? zip;
  String? country;
  String? contactPerson;
  String? contactEmail;
  String? contactPhone;
  bool? isActive;
  bool? dvir;
  bool? bol;
  bool? accountDeactivation;
  bool? trailerAudit;
  Audit? audit;
  String? timeZone;
  int? overTime;
  bool? trailerStandPhoto;

  ClientDetailsNewModel({
    this.clientId,
    this.clientName,
    this.street,
    this.city,
    this.state,
    this.zip,
    this.country,
    this.contactPerson,
    this.contactEmail,
    this.contactPhone,
    this.isActive,
    this.dvir,
    this.bol,
    this.accountDeactivation,
    this.trailerAudit,
    this.audit,
    this.timeZone,
    this.overTime,
    this.trailerStandPhoto,
  });

  factory ClientDetailsNewModel.fromJson(Map<String, dynamic> json) =>
      ClientDetailsNewModel(
        clientId: json["clientId"],
        clientName: json["clientName"],
        street: json["street"],
        city: json["city"],
        state: json["state"],
        zip: json["zip"],
        country: json["country"],
        contactPerson: json["contactPerson"],
        contactEmail: json["contactEmail"],
        contactPhone: json["contactPhone"],
        isActive: json["isActive"],
        dvir: json["dvir"],
        bol: json["bol"],
        accountDeactivation: json["accountDeactivation"],
        trailerAudit: json["trailerAudit"],
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
        timeZone: json["timeZone"],
        overTime: json["overTime"],
        trailerStandPhoto: json["trailerStandPhoto"],
      );

  Map<String, dynamic> toJson() => {
        "clientId": clientId,
        "clientName": clientName,
        "street": street,
        "city": city,
        "state": state,
        "zip": zip,
        "country": country,
        "contactPerson": contactPerson,
        "contactEmail": contactEmail,
        "contactPhone": contactPhone,
        "isActive": isActive,
        "dvir": dvir,
        "bol": bol,
        "accountDeactivation": accountDeactivation,
        "trailerAudit": trailerAudit,
        "audit": audit?.toJson(),
        "timeZone": timeZone,
        "overTime": overTime,
        "trailerStandPhoto": trailerStandPhoto,
      };
}

class Audit {
  String? createdDate;
  String? lastModifiedDate;
  EdBy? createdBy;
  EdBy? lastModifiedBy;

  Audit({
    this.createdDate,
    this.lastModifiedDate,
    this.createdBy,
    this.lastModifiedBy,
  });

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
        createdBy:
            json["createdBy"] == null ? null : EdBy.fromJson(json["createdBy"]),
        lastModifiedBy: json["lastModifiedBy"] == null
            ? null
            : EdBy.fromJson(json["lastModifiedBy"]),
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
        "createdBy": createdBy?.toJson(),
        "lastModifiedBy": lastModifiedBy?.toJson(),
      };
}

class EdBy {
  String? userId;
  String? firstName;
  String? lastName;
  String? email;
  String? phone;
  String? timeZone;
  String? lastLoginTime;
  bool? isActive;
  bool? isAdUser;

  EdBy({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
    this.isActive,
    this.isAdUser,
  });

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
        isActive: json["isActive"],
        isAdUser: json["isADUser"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
        "isActive": isActive,
        "isADUser": isAdUser,
      };
}
