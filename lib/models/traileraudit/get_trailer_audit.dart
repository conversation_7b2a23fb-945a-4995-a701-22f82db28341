// To parse this JSON data, do
//
//     final getTrailerAudit = getTrailerAuditFromJson(jsonString);

import 'dart:convert';

GetTrailerAudit getTrailerAuditFromJson(String str) =>
    GetTrailerAudit.fromJson(json.decode(str));

String getTrailerAuditToJson(GetTrailerAudit data) =>
    json.encode(data.toJson());

class GetTrailerAudit {
  List<ListElement>? list;
  int? page;
  int? size;
  int? totalElements;

  GetTrailerAudit({
    this.list,
    this.page,
    this.size,
    this.totalElements,
  });

  factory GetTrailerAudit.fromJson(Map<String, dynamic> json) =>
      GetTrailerAudit(
        list: json["list"] == null
            ? []
            : List<ListElement>.from(
                json["list"]!.map((x) => ListElement.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": list == null
            ? []
            : List<dynamic>.from(list!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class ListElement {
  String? area;
  String? slot;
  String? carrier;
  String? trailerNumber;
  String? trailerStatus;
  String? notes;
  Audit? audit;

  ListElement({
    this.area,
    this.slot,
    this.carrier,
    this.trailerNumber,
    this.trailerStatus,
    this.notes,
    this.audit,
  });

  factory ListElement.fromJson(Map<String, dynamic> json) => ListElement(
        area: json["area"],
        slot: json["slot"],
        carrier: json["carrier"],
        trailerNumber: json["trailerNumber"],
        trailerStatus: json["trailerStatus"],
        notes: json["notes"],
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
      );

  Map<String, dynamic> toJson() => {
        "area": area,
        "slot": slot,
        "carrier": carrier,
        "trailerNumber": trailerNumber,
        "trailerStatus": trailerStatus,
        "notes": notes,
        "audit": audit?.toJson(),
      };
}

class Audit {
  String? createdDate;
  String? lastModifiedDate;
  EdBy? createdBy;
  EdBy? lastModifiedBy;

  Audit({
    this.createdDate,
    this.lastModifiedDate,
    this.createdBy,
    this.lastModifiedBy,
  });

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
        createdBy:
            json["createdBy"] == null ? null : EdBy.fromJson(json["createdBy"]),
        lastModifiedBy: json["lastModifiedBy"] == null
            ? null
            : EdBy.fromJson(json["lastModifiedBy"]),
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
        "createdBy": createdBy?.toJson(),
        "lastModifiedBy": lastModifiedBy?.toJson(),
      };
}

class EdBy {
  String? userId;
  String? firstName;
  String? lastName;
  String? email;
  String? phone;
  String? timeZone;
  String? lastLoginTime;
  bool? isActive;

  EdBy({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
    this.isActive,
  });

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
        isActive: json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
        "isActive": isActive,
      };
}
