// To parse this JSON data, do
//
//     final postTrailerAudit = postTrailerAuditFromJson(jsonString);

import 'dart:convert';

PostTrailerAudit postTrailerAuditFromJson(String str) =>
    PostTrailerAudit.fromJson(json.decode(str));

String postTrailerAuditToJson(PostTrailerAudit data) =>
    json.encode(data.toJson());

class PostTrailerAudit {
  String? area;
  String? slot;
  String? fleetId;
  String? trailerStatus;
  String? notes;
  String? carrier;
  String? locationId;
  String? spotId;

  PostTrailerAudit(
      {this.area,
      this.slot,
      this.fleetId,
      this.trailerStatus,
      this.notes,
      this.carrier,
      this.locationId,
      this.spotId});

  factory PostTrailerAudit.fromJson(Map<String, dynamic> json) =>
      PostTrailerAudit(
        area: json["area"],
        slot: json["slot"],
        fleetId: json["fleetId"],
        trailerStatus: json["trailerStatus"],
        notes: json["notes"],
        carrier: json["carrier"],
        locationId: json["locationId"],
        spotId: json["spotId"],
      );

  Map<String, dynamic> toJson() => {
        "area": area,
        "slot": slot,
        "fleetId": fleetId,
        "trailerStatus": trailerStatus,
        "notes": notes,
        "carrier": carrier,
        "locationId": locationId,
        "spotId": spotId
      };
}
