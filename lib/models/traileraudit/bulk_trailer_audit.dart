// To parse this JSON data, do
//
//     final bulkTrailerAudit = bulkTrailerAuditFromJson(jsonString);

import 'dart:convert';

List<BulkTrailerAuditItem> bulkTrailerAuditFromJson(String str) =>
    List<BulkTrailerAuditItem>.from(
        json.decode(str).map((x) => BulkTrailerAuditItem.fromJson(x)));

String bulkTrailerAuditToJson(List<BulkTrailerAuditItem> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class BulkTrailerAuditItem {
  String? fleetId;
  String? trailerStatus;
  String? notes;
  String? spotId;

  BulkTrailerAuditItem({
    this.fleetId,
    this.trailerStatus,
    this.notes,
    this.spotId,
  });

  factory BulkTrailerAuditItem.fromJson(Map<String, dynamic> json) =>
      BulkTrailerAuditItem(
        fleetId: json["fleetId"],
        trailerStatus: json["trailerStatus"],
        notes: json["notes"],
        spotId: json["spotId"],
      );

  Map<String, dynamic> toJson() => {
        "fleetId": fleetId,
        "trailerStatus": trailerStatus,
        "notes": notes,
        "spotId": spotId,
      };
}
