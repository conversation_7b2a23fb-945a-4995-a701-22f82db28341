// To parse this JSON data, do
//
//     final truckListModel = truckListModelFromJson(jsonString);

import 'dart:convert';

import 'package:spot_on/models/spots_list.dart';

TruckListModel truckListModelFromJson(String str) =>
    TruckListModel.fromJson(json.decode(str));

String truckListModelToJson(TruckListModel data) => json.encode(data.toJson());

class TruckListModel {
  TruckListModel({
    this.list = const [],
    this.page = 0,
    this.size = 0,
    this.totalElements = 0,
  });

  final List<TruckDetail> list;
  final int page;
  final int size;
  final int totalElements;

  factory TruckListModel.fromJson(Map<String, dynamic> json) => TruckListModel(
        list: json["list"] == null
            ? []
            : List<TruckDetail>.from(
                json["list"].map((x) => TruckDetail.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": list == null
            ? null
            : List<dynamic>.from(list.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class TruckDetail {
  TruckDetail({
    this.fleetId,
    this.plateNumber,
    this.year,
    this.make,
    this.model,
    this.color,
    this.carrier,
    this.type,
    this.remarks,
    this.isActive,
    this.audit,
    this.spot,
    this.unitNumber,
    this.isHotTrailer = false,
    this.inTransit = false,
    this.fleetStatus,
  });

  final String? fleetId;
  final String? plateNumber;
  final int? year;
  final String? make;
  final String? model;
  final String? color;
  final String? carrier;
  final String? type;
  final String? remarks;
  final bool? isActive;
  final Audit? audit;
  final Spot? spot;
  final String? unitNumber;
  final bool isHotTrailer;
  bool inTransit;
  final String? fleetStatus;

  factory TruckDetail.fromJson(Map<String, dynamic> json) => TruckDetail(
        fleetId: json["fleetId"],
        plateNumber: json["plateNumber"],
        year: json["year"],
        make: json["make"],
        model: json["model"],
        color: json["color"],
        carrier: json["carrier"],
        type: json["type"],
        remarks: json["remarks"],
        isActive: json["isActive"],
        unitNumber: json["unitNumber"],
        audit: json["audit"] == null ? null : Audit.fromJson(json["audit"]),
        spot: json["spot"] == null ? null : Spot.fromJson(json["spot"]),
        isHotTrailer: json['isHotTrailer'] ?? false,
        inTransit: json['inTransit'] ?? false,
        fleetStatus: json["fleetStatus"],
      );

  Map<String, dynamic> toJson() => {
        "fleetId": fleetId,
        "plateNumber": plateNumber,
        "year": year,
        "make": make,
        "model": model,
        "color": color,
        "carrier": carrier,
        "type": type,
        "remarks": remarks,
        "isActive": isActive,
        "isHotTrailer": isHotTrailer,
        "inTransit": inTransit,
        "audit": audit?.toJson(),
        "unitNumber": unitNumber,
        "fleetStatus": fleetStatus,
      };
}

class Audit {
  Audit({
    this.createdDate,
    this.lastModifiedDate,
    this.createdBy,
    this.lastModifiedBy,
  });

  final String? createdDate;
  final String? lastModifiedDate;
  final EdBy? createdBy;
  final EdBy? lastModifiedBy;

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdDate: json["createdDate"],
        lastModifiedDate: json["lastModifiedDate"],
        createdBy:
            json["createdBy"] == null ? null : EdBy.fromJson(json["createdBy"]),
        lastModifiedBy: json["lastModifiedBy"] == null
            ? null
            : EdBy.fromJson(json["lastModifiedBy"]),
      );

  Map<String, dynamic> toJson() => {
        "createdDate": createdDate,
        "lastModifiedDate": lastModifiedDate,
        "createdBy": createdBy?.toJson(),
        "lastModifiedBy":
            lastModifiedBy?.toJson(),
      };
}

class EdBy {
  EdBy({
    this.userId,
    this.firstName,
    this.lastName,
    this.email,
    this.phone,
    this.timeZone,
    this.lastLoginTime,
  });

  final String? userId;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? phone;
  final String? timeZone;
  final String? lastLoginTime;

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        userId: json["userId"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        phone: json["phone"],
        timeZone: json["timeZone"],
        lastLoginTime: json["lastLoginTime"],
      );

  Map<String, dynamic> toJson() => {
        "userId": userId,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "phone": phone,
        "timeZone": timeZone,
        "lastLoginTime": lastLoginTime,
      };
}
