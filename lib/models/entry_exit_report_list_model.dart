// To parse this JSON data, do
//
//     final entryExitReportListModel = entryExitReportListModelFromJson(jsonString);

import 'dart:convert';

EntryExitReportListModel entryExitReportListModelFromJson(String str) =>
    EntryExitReportListModel.fromJson(json.decode(str));

String entryExitReportListModelToJson(EntryExitReportListModel data) =>
    json.encode(data.toJson());

class EntryExitReportListModel {
  EntryExitReportListModel({
    required this.list,
    required this.page,
    required this.size,
    required this.totalElements,
  });

  final List<EntryExit> list;
  final int page;
  final int size;
  final int totalElements;

  factory EntryExitReportListModel.fromJson(Map<String, dynamic> json) =>
      EntryExitReportListModel(
        list: json["list"] == null
            ? []
            : List<EntryExit>.from(
                json["list"].map((x) => EntryExit.fromJson(x))),
        page: json["page"],
        size: json["size"],
        totalElements: json["totalElements"],
      );

  Map<String, dynamic> toJson() => {
        "list": list == null
            ? null
            : List<dynamic>.from(list.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "totalElements": totalElements,
      };
}

class EntryExit {
  EntryExit({
    required this.audit,
    required this.fleet,
    required this.guardEntryExitId,
    required this.location,
    required this.notes,
    required this.type,
  });

  final Audit? audit;
  final Fleet fleet;
  final String guardEntryExitId;
  final Location? location;
  final String? notes;
  final String? type;

  factory EntryExit.fromJson(Map<String, dynamic> json) => EntryExit(
        audit: null == json["audit"] ? null : Audit.fromJson(json["audit"]),
        fleet: Fleet.fromJson(json["fleet"]),
        guardEntryExitId: json["guardEntryExitId"],
        location: Location.fromJson(json["location"]),
        notes: json["notes"],
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "audit": audit?.toJson(),
        "fleet": fleet.toJson(),
        "guardEntryExitId": guardEntryExitId,
        "location": location?.toJson(),
        "notes": notes,
        "type": type,
      };
}

class Audit {
  Audit({
    required this.createdBy,
    required this.createdDate,
    required this.lastModifiedBy,
    required this.lastModifiedDate,
  });

  final EdBy? createdBy;
  final String createdDate;
  final EdBy? lastModifiedBy;
  final String lastModifiedDate;

  factory Audit.fromJson(Map<String, dynamic> json) => Audit(
        createdBy: null == json["createdBy"]
            ? EdBy(
                email: '',
                clients: [],
                firstName: '',
                isActive: false,
                lastLoginTime: '',
                lastName: '',
                phone: '',
                roles: [],
                timeZone: '',
                userId: '',
              )
            : EdBy.fromJson(json["createdBy"]),
        createdDate: json["createdDate"],
        lastModifiedBy: EdBy.fromJson(json["lastModifiedBy"]),
        lastModifiedDate: json["lastModifiedDate"],
      );

  Map<String, dynamic> toJson() => {
        "createdBy": createdBy,
        "createdDate": createdDate,
        "lastModifiedBy": lastModifiedBy,
        "lastModifiedDate": lastModifiedDate,
      };
}

class EdBy {
  EdBy({
    required this.clients,
    required this.email,
    required this.firstName,
    required this.isActive,
    required this.lastLoginTime,
    required this.lastName,
    required this.phone,
    required this.roles,
    required this.timeZone,
    required this.userId,
  });

  final List<Client> clients;
  final String email;
  final String firstName;
  final bool isActive;
  final String lastLoginTime;
  final String lastName;
  final String? phone;
  final List<Role> roles;
  final String timeZone;
  final String userId;

  factory EdBy.fromJson(Map<String, dynamic> json) => EdBy(
        clients: json["clients"] == null
            ? []
            : List<Client>.from(json["clients"].map((x) => Client.fromJson(x))),
        email: json["email"],
        firstName: json["firstName"],
        isActive: json["isActive"],
        lastLoginTime: json["lastLoginTime"],
        lastName: json["lastName"],
        phone: json["phone"],
        roles: json["roles"] == null
            ? []
            : List<Role>.from(json["roles"].map((x) => Role.fromJson(x))),
        timeZone: json["timeZone"],
        userId: json["userId"],
      );

  Map<String, dynamic> toJson() => {
        "clients": clients == null
            ? null
            : List<dynamic>.from(clients.map((x) => x.toJson())),
        "email": email,
        "firstName": firstName,
        "isActive": isActive,
        "lastLoginTime": lastLoginTime,
        "lastName": lastName,
        "phone": phone,
        "roles": roles == null
            ? null
            : List<dynamic>.from(roles.map((x) => x.toJson())),
        "timeZone": timeZone,
        "userId": userId,
      };
}

class Client {
  Client({
    required this.city,
    required this.clientId,
    required this.clientName,
    required this.contactEmail,
    required this.contactPerson,
    required this.contactPhone,
    required this.country,
    required this.isActive,
    required this.remarks,
    required this.state,
    required this.street,
    required this.zip,
  });

  final String city;
  final String clientId;
  final String clientName;
  final String contactEmail;
  final String contactPerson;
  final String contactPhone;
  final String country;
  final bool isActive;
  final String remarks;
  final String state;
  final String street;
  final String zip;

  factory Client.fromJson(Map<String, dynamic> json) => Client(
        city: json["city"],
        clientId: json["clientId"],
        clientName: json["clientName"],
        contactEmail: json["contactEmail"],
        contactPerson: json["contactPerson"],
        contactPhone: json["contactPhone"],
        country: json["country"],
        isActive: json["isActive"],
        remarks: json["remarks"],
        state: json["state"],
        street: json["street"],
        zip: json["zip"],
      );

  Map<String, dynamic> toJson() => {
        "city": city,
        "clientId": clientId,
        "clientName": clientName,
        "contactEmail": contactEmail,
        "contactPerson": contactPerson,
        "contactPhone": contactPhone,
        "country": country,
        "isActive": isActive,
        "remarks": remarks,
        "state": state,
        "street": street,
        "zip": zip,
      };
}

class Role {
  Role({
    required this.isActive,
    required this.roleId,
    required this.roleName,
  });

  final bool isActive;
  final String roleId;
  final String roleName;

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        isActive: json["isActive"],
        roleId: json["roleId"],
        roleName: json["roleName"],
      );

  Map<String, dynamic> toJson() => {
        "isActive": isActive,
        "roleId": roleId,
        "roleName": roleName,
      };
}

class Fleet {
  Fleet({
    required this.audit,
    required this.carrier,
    required this.fleetId,
    required this.fleetStatus,
    required this.isActive,
    required this.isHotTrailer,
    required this.owner,
    required this.remarks,
    required this.type,
    required this.unitNumber,
  });

  final Audit? audit;
  final String carrier;
  final String fleetId;
  final String fleetStatus;
  final bool isActive;
  final bool? isHotTrailer;
  final String? owner;
  final String? remarks;
  final String? type;
  final String? unitNumber;

  factory Fleet.fromJson(Map<String, dynamic> json) => Fleet(
        audit: null == json["audit"] ? null : Audit.fromJson(json["audit"]),
        carrier: json["carrier"],
        fleetId: json["fleetId"],
        fleetStatus: json["fleetStatus"],
        isActive: json["isActive"],
        isHotTrailer: json["isHotTrailer"],
        owner: json["owner"],
        remarks: json["remarks"],
        type: json["type"],
        unitNumber: json["unitNumber"],
      );

  Map<String, dynamic> toJson() => {
        "audit": audit?.toJson(),
        "carrier": carrier,
        "fleetId": fleetId,
        "fleetStatus": fleetStatus,
        "isActive": isActive,
        "isHotTrailer": isHotTrailer,
        "owner": owner,
        "remarks": remarks,
        "type": type,
        "unitNumber": unitNumber,
      };
}

class Location {
  Location({
    required this.audit,
    required this.city,
    required this.clientId,
    required this.country,
    required this.isActive,
    required this.isDefault,
    required this.latitude,
    required this.locationId,
    required this.locationMapJson,
    required this.locationName,
    required this.longitude,
    required this.mapImageUrl,
    required this.remarks,
    required this.state,
    required this.street,
    required this.zip,
  });

  final Audit? audit;
  final String city;
  final String clientId;
  final String country;
  final bool isActive;
  final bool isDefault;
  final double latitude;
  final String locationId;
  final String locationMapJson;
  final String locationName;
  final double longitude;
  final String mapImageUrl;
  final String remarks;
  final String state;
  final String street;
  final String zip;

  factory Location.fromJson(Map<String, dynamic> json) => Location(
        audit: null == json["audit"] ? null : Audit.fromJson(json["audit"]),
        city: json["city"],
        clientId: json["clientId"],
        country: json["country"],
        isActive: json["isActive"],
        isDefault: json["isDefault"],
        latitude: json["latitude"],
        locationId: json["locationId"],
        locationMapJson: json["locationMapJson"],
        locationName: json["locationName"],
        longitude: json["longitude"],
        mapImageUrl: json["mapImageUrl"],
        remarks: json["remarks"],
        state: json["state"],
        street: json["street"],
        zip: json["zip"],
      );

  Map<String, dynamic> toJson() => {
        "audit": audit?.toJson(),
        "city": city,
        "clientId": clientId,
        "country": country,
        "isActive": isActive,
        "isDefault": isDefault,
        "latitude": latitude,
        "locationId": locationId,
        "locationMapJson": locationMapJson,
        "locationName": locationName,
        "longitude": longitude,
        "mapImageUrl": mapImageUrl,
        "remarks": remarks,
        "state": state,
        "street": street,
        "zip": zip,
      };
}
