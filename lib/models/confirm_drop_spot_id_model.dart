// To parse this JSON data, do
//
//     final confirmDropUpdateDropModel = confirmDropUpdateDropModelFromJson(jsonString);

import 'dart:convert';

ConfirmDropUpdateDropModel confirmDropUpdateDropModelFromJson(String str) =>
    ConfirmDropUpdateDropModel.fromJson(json.decode(str));

String confirmDropUpdateDropModelToJson(ConfirmDropUpdateDropModel data) =>
    json.encode(data.toJson());

class ConfirmDropUpdateDropModel {
  ConfirmDropUpdateDropModel({
    required this.sequenceAsn,
    required this.assignedToUserId,
    required this.description,
    required this.dropLocationId,
    this.dropSpotId,
    required this.fleetId,
    required this.fleetStatus,
    required this.pickupLocationId,
    this.pickupSpotId,
    required this.priority,
  });
  final String sequenceAsn;

  final String assignedToUserId;
  final String description;
  final String dropLocationId;
  final String? dropSpotId;
  final String fleetId;
  String? fleetStatus;
  final String pickupLocationId;
  final String? pickupSpotId;
  final String priority;

  factory ConfirmDropUpdateDropModel.fromJson(Map<String, dynamic> json) =>
      ConfirmDropUpdateDropModel(
        sequenceAsn: json["sequenceAsn"],
        assignedToUserId: json["assignedToUserId"],
        description: json["description"],
        dropLocationId: json["dropLocationId"],
        dropSpotId: json["dropSpotId"],
        fleetId: json["fleetId"],
        fleetStatus: json["fleetStatus"],
        pickupLocationId: json["pickupLocationId"],
        pickupSpotId: json["pickupSpotId"],
        priority: json["priority"],
      );

  Map<String, dynamic> toJson() => {
        "sequenceAsn": sequenceAsn,
        "assignedToUserId": assignedToUserId,
        "description": description,
        "dropLocationId": dropLocationId,
        "dropSpotId": dropSpotId,
        "fleetId": fleetId,
        "fleetStatus": fleetStatus,
        "pickupLocationId": pickupLocationId,
        "pickupSpotId": pickupSpotId,
        "priority": priority,
      };
}
