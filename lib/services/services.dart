import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http_parser/http_parser.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:http/http.dart';
import 'package:platform_device_id/platform_device_id.dart';
import 'package:spot_on/models/carriers_list.dart';
import 'package:spot_on/models/cliient_list_model.dart';
import 'package:spot_on/models/confirm_drop_spot_id_model.dart';
import 'package:spot_on/models/createjob_list.dart';
import 'package:spot_on/models/driverlistentryexit.dart';
import 'package:spot_on/models/drivers_list.dart';
import 'package:spot_on/models/entry_exit_report_list_model.dart';
import 'package:spot_on/models/error_model.dart';
import 'package:spot_on/models/job/job_request_model.dart';
import 'package:spot_on/models/job/jobs_list_model.dart';
import 'package:spot_on/models/login/login_response_model.dart';
import 'package:spot_on/models/new_trailer_req_model.dart';
import 'package:spot_on/models/profile_update_model.dart';
import 'package:spot_on/models/spot_list_entryexit.dart';
import 'package:spot_on/models/spot_on_locations.dart';
import 'package:spot_on/models/spots_list.dart';
import 'package:spot_on/models/traileraudit/get_trailer_audit.dart';
import 'package:spot_on/models/traileraudit/post_trailer_audit.dart';
import 'package:spot_on/models/traileraudit/bulk_trailer_audit.dart';
import 'package:spot_on/models/truck_list_model.dart';
import 'package:spot_on/models/user_info_model.dart';
import 'package:spot_on/utils/imports.dart';

// import '../models/addnew/carrieradd.dar;
// import '../models/addnew/supplieradd.dart';t'
import '../models/autopopulateexit.dart';
import '../models/carrieradd.dart';
import '../models/client_details_new_model.dart';
import '../models/client_listentryexit.dart';
import '../models/createjob_autofill.dart';
import '../models/damage/damage_report.dart';
// import '../models/driver_listentryexit.dart';
import '../models/location_listentryexit.dart';
import '../models/supplieradd.dart';
import '../models/supplierlist.dart';
import '../utils/app_logger.dart';

class Services {
  // staging
  // static const String baseUrl = 'https://api.spoton-northstar.com';
  // static var termsUrl = "https://app.spoton-northstar.com/#/terms";
  // static var privacyUrl = "https://app.spoton-northstar.com/#/privacy-policy";
//infra
  //static const String baseUrl = 'https://api.infra.spoton-northstar.com';
  // static const String baseUrl = 'https://api.uat.spoton-northstar.com';

  //prod
  static const String baseUrl = 'https://api.ablair-spoton.com';
  static var termsUrl = "https://app.ablair-spoton.com/#/terms";
  static var privacyUrl = "https://app.ablair-spoton.com/#/privacy-policy";

  static var clientId = "spoton-clients";
  static var clientSecret = "spoton-clients-secret@2022";
  static const _authHeader =
      'Basic c3BvdG9uLWNsaWVudHM6c3BvdG9uLWNsaWVudHMtc2VjcmV0QDIwMjI=';

  // static InterceptedClient client = InterceptedClient.build(interceptors: [
  //   SpotOnInterceptor(),
  // ]);

  static Future loginUser(String username, String password) async {
    try {
      String url = "$baseUrl/oauth/token";
      // printLog('Login: $url, $username, $password');
      // printLog({
      //   "client_id": clientId,
      //   "client_secret": clientSecret,
      //   "grant_type": "password",
      //   "username": username,
      //   "password": password,
      // });
      // "client_id": clientId,
      // "client_secret": clientSecret,
      Response response = await post(
        Uri.parse(url),
        body: {
          "grant_type": "password",
          "username": username,
          "password": password,
        },
        headers: {
          HttpHeaders.contentTypeHeader: 'application/x-www-form-urlencoded',
          HttpHeaders.authorizationHeader: _authHeader
        },
      );
      // printLog(response.body);
      log(response.body);
      if (response.statusCode == 200) {
        LoginResponseModel loginResponseModel =
            loginResponseModelFromJson(response.body);
        return Success(code: response.statusCode, response: loginResponseModel);
      }
      return Failure(
          code: -1, response: 'Please Enter Valid E-Mail Address or Password');
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future doLogout() async {
    try {
      FirebaseMessaging.instance.deleteToken();
    } catch (e) {
      // printLog("Failed to delete fcm token");
    }
    String? deviceUuid = await PlatformDeviceId.getDeviceId;
    String url = "$baseUrl/v1/oauth/token?deviceUuid=$deviceUuid";
    // printLog(url);
    Map<String, String> headers = {
      HttpHeaders.contentTypeHeader: 'application/json',
      "Authorization": "BEARER ${Preferences.loginResponseModel!.accessToken}"
    };
    var response = await delete(Uri.parse(url),
        body: jsonEncode({
          "deviceUuid": deviceUuid,
        }),
        headers: headers);
    return Success(code: response.statusCode, response: true);
  }

  static Future getClients() async {
    try {
      String url = "$baseUrl/v1/clients?isActive=true&page=0&size=50";
      // printLog('getClients: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == 200) {
        ClientListModel clientListModel =
            clientListModelFromJson(response.body);
        return Success(code: response.statusCode, response: clientListModel);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getDriversList(String loc1, String loc2) async {
    SpotOnClient? client = await Preferences.getSelectedClient();

    try {
      String url;
      String locationParams = '';

      if (loc1.isNotEmpty) {
        locationParams += '&locationIds=$loc1';
      }

      if (loc2.isNotEmpty) {
        locationParams += ',$loc2'; // Add comma only if loc1 exists
      }

      if (locationParams.isNotEmpty) {
        url =
            "$baseUrl/v1/clients/${client?.clientId}/users?isActive=true&page=0&size=1000&sort=createdDate,desc$locationParams";
      } else {
        url =
            "$baseUrl/v1/users?isActive=true&page=0&size=1000&sort=createdDate,desc";
      }
      // String url;
      // if (loc1.isNotEmpty) {
      //   url =
      //       "$baseUrl/v1/clients/${client?.clientId}/users?isActive=true&page=0&size=1000&sort=createdDate,desc&locationIds=$loc1,$loc2";
      // } else {
      //  url= "$baseUrl/v1/users?isActive=true&page=0&size=1000&sort=createdDate,desc";
      // }
      // printLog('getDriversList: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        DriversList driversList = driversListFromJson(response.body);
        return Success(code: response.statusCode, response: driversList);
      }
      return _getFailure(response);
    } catch (e) {
      printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static _getFailure(Response response) {
    ErrorModel errorModel = errorModelFromJson(response.body);
    return Failure(
        code: response.statusCode, response: errorModel.errors![0].message!);
  }

  // static Future getSpotsOld(String clientId, String locationId) async {
  //   try {
  //     String url =
  //         "$baseUrl/v1/clients/$clientId/spots?location.uuid=$locationId&isActive=true&size=1000&sort=spotName";
  //     // printLog('getSpots: $url');
  //     Response response = await get(
  //       Uri.parse(url),
  //       headers: {
  //         HttpHeaders.contentTypeHeader: 'application/json',
  //         "Authorization":
  //             "BEARER ${Preferences.loginResponseModel!.accessToken}"
  //       },
  //     );
  //     // printLog(response.body);
  //     if (response.statusCode == unauthorized) {
  //       return Failure(code: unauthorized, response: 'Unauthorized');
  //     }
  //     if (response.statusCode == 200) {
  //       SpotsList spotsList = spotsListFromJson(response.body);
  //       return Success(code: response.statusCode, response: spotsList);
  //     }
  //     return _getFailure(response);
  //   } catch (e) {
  //     // printLog(e.toString());
  //     return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
  //   }
  // }

  static Future getSpots(String clientId, String locationId) async {
    try {
      String url =
          "$baseUrl/v1/clients/$clientId/spots/dropdown?location.uuid=$locationId&isActive=true&size=1000&sort=spotName";
      // printLog('getSpots: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        SpotsList spotsList = spotsListFromJson(response.body);
        return Success(code: response.statusCode, response: spotsList);
      }
      return _getFailure(response);
    } catch (e) {
      printLog('jino--------67--${e.toString()}');
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getLocationSpots(String clientId, String id) async {
    print('drop123');
    String url;
    try {
      if (id.isNotEmpty) {
        url =
            "$baseUrl/v1/clients/$clientId/locations?isActive=true&userId=$id";
      } else {
        url = "$baseUrl/v1/clients/$clientId/locations?isActive=true";
      }
      // printLog('getLocationSpots: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        SpotOnLocationList spotOnLocationList =
            spotOnLocationListFromJson(response.body);
        return Success(code: response.statusCode, response: spotOnLocationList);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getDropOffLocations(String clientId) async {
    print('drop2222');
    try {
      String url = "$baseUrl/v1/clients/$clientId/locations?isActive=true";
      // printLog('getLocationSpots: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        PLocation spotOnLocationList =
            PLocation.fromJson(jsonDecode(response.body));
        return Success(code: response.statusCode, response: spotOnLocationList);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getTruckList() async {
    try {
      String url =
          "$baseUrl/v1/fleets?isActive=true&page=0&size=2000&sort=type,desc";
      // printLog('getTruckList: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        TruckListModel truckListModel = truckListModelFromJson(response.body);
        return Success(code: response.statusCode, response: truckListModel);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getTruckListNew(String unitNumber) async {
    try {
      String url =
          "$baseUrl/v1/fleets?isActive=true&page=0&unitNumber=$unitNumber&size=2000&sort=type,desc";
      // printLog('getTruckListNew: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        TruckListModel truckListModel = truckListModelFromJson(response.body);
        return Success(code: response.statusCode, response: truckListModel);
      }
      print('value-----${response.body.toString()}');
      return _getFailure(response);
    } catch (e) {
      printLog('val--pro--${e.toString()}');
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getTruckListEntryExit(String unitNumber) async {
    try {
      String url =
          "$baseUrl/v1/fleets?page=0&unitNumber=$unitNumber&size=2000&sort=type,desc";
      // printLog('getTruckListNew: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        TruckListModel truckListModel = truckListModelFromJson(response.body);
        return Success(code: response.statusCode, response: truckListModel);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future getCarrierPatternListEntryExit(String pattern) async {
    try {
      String url =
          "$baseUrl/v1/carrier?isActive=true&page=0&size=10000&carrier=$pattern";
      // printLog('getTruckListNew: $url');
      var response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        List<String> carrierList = carrierListFromJson(response.body);

        return carrierList;
      }
      return _getFailure(response);
    } catch (e) {
      printLog('1-100${e.toString()}');
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future getCarrierListEntryExit() async {
    try {
      String url = "$baseUrl/v1/carrier?isActive=true&page=0&size=10000";
      // printLog('getTruckListNew: $url');
      var response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        List<String> carrierList = carrierListFromJson(response.body);
        return carrierList;
      }
      return _getFailure(response);
    } catch (e) {
      printLog('1-101${e.toString()}');
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future getCarrierSearchListEntryExit(String pattern) async {
    try {
      String url = "$baseUrl/v1/carrier/$pattern";
      // printLog('getTruckListNew: $url');
      var response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        List<String> carrierList = carrierListFromJson(response.body);
        return carrierList;
      }
      return _getFailure(response);
    } catch (e) {
      printLog('1-102${e.toString()}');
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future addCarrierListEntryExit(String text) async {
    try {
      String url = "$baseUrl/v1/carrier";
      var params = {"carrier": text};
      // printLog('getTruckListNew: $url');
      Response response = await post(Uri.parse(url),
          headers: {
            HttpHeaders.contentTypeHeader: 'application/json',
            "Authorization":
                "BEARER ${Preferences.loginResponseModel!.accessToken}"
          },
          body: jsonEncode(params));
      // printLog(response.body);
      // if (response.statusCode == unauthorized) {
      //   return Failure(code: unauthorized, response: 'Unauthorized');
      // }
      if (response.statusCode == 201) {
        // CarrierSuccess carrier=response;
        //  CarrierList carrierList= carrierListFromJson(response.body);
        return CarrierSuccess.fromJson(jsonDecode(response.body));
      }
      return CarrierSuccess.fromJson(
          {"carrierId": "", "carrier": "", "id": 321});
    } catch (e) {
      // printLog(e.toString());
      return CarrierSuccess.fromJson(
          {"carrierId": "", "carrier": "", "id": 321});
    }
  }

  Future addSupplierListEntryExit(String text) async {
    try {
      String url = "$baseUrl/v1/supplier";
      var params = {"supplier": text};
      // printLog('getTruckListNew: $url');
      Response response = await post(Uri.parse(url),
          headers: {
            HttpHeaders.contentTypeHeader: 'application/json',
            "Authorization":
                "BEARER ${Preferences.loginResponseModel!.accessToken}"
          },
          body: jsonEncode(params));
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 201) {
        //  CarrierList carrierList= carrierListFromJson(response.body);
        return SupplierSuccess.fromJson(jsonDecode(response.body));
      }
      return SupplierSuccess.fromJson(
          {"supplierId": "", "supplier": "", "id": 38});
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future getSupplierListEntryExit() async {
    try {
      String url = "$baseUrl/v1/supplier?isActive=true&page=0&size=10000";
      // printLog('getTruckListNew: $url');
      var response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        SupplierList supplierList =
            SupplierList.fromJson(json.decode(response.body));
        return supplierList;
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future getSupplierSearchListEntryExit(String pattern) async {
    try {
      String url =
          "$baseUrl/v1/supplier?isActive=true&page=0&size=10000&supplier=$pattern";
      // printLog('getTruckListNew: $url');
      var response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        SupplierList supplierList =
            SupplierList.fromJson(json.decode(response.body));
        return supplierList;
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future getSpotListEntryExit(String loc) async {
    try {
      SpotOnClient? spotOnClient = await Preferences.getSelectedClient();
      String url =
          "$baseUrl/v1/clients/${spotOnClient?.clientId}/spots/dropdown?isActive=true&page=0&size=1000&location.uuid=$loc&sort=spotName";
      // String url = "$baseUrl/v1/clients/${spotOnClient?.clientId}/spots";
      // printLog('getTruckListNew: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );

      if (response.statusCode == 200) {
        print('risaj----${response.body}');
        SpotListeEntryExit spotList =
            SpotListeEntryExit.fromJson(json.decode(response.body));
        print('risaj-spot---${spotList.list}');

        return spotList;
      }
    } catch (e) {
      printLog('error-occured--22-${e.toString()}');
      // return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future getSpotListCreateJob(String loc) async {
    try {
      SpotOnClient? spotOnClient = await Preferences.getSelectedClient();
      String url =
          "$baseUrl/v1/clients/${spotOnClient?.clientId}/spots/dropdown?isActive=true&page=0&size=1000&location.uuid=$loc&sort=spotName";
      // String url = "$baseUrl/v1/clients/${spotOnClient?.clientId}/spots";
      // printLog('getTruckListNew: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );

      if (response.statusCode == 200) {
        print('risaj----${response.body}');
        CreateJobSpotList spotList =
            CreateJobSpotList.fromJson(json.decode(response.body));
        print('risaj-spot---${spotList.list}');

        return spotList;
      }
    } catch (e) {
      printLog('error-occured--22-${e.toString()}');
      // return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future getLocationListEntryExit() async {
    try {
      SpotOnClient? spotOnClient = await Preferences.getSelectedClient();
      String url =
          "$baseUrl/v1/clients/${spotOnClient?.clientId}/locations?isActive=true&page=0&size=10000";
      // printLog('getTruckListNew: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );

      if (response.statusCode == 200) {
        print('risaj----${response.body}');
        LocationListeEntryExit locList =
            LocationListeEntryExit.fromJson(json.decode(response.body));
        print('risaj-spot---$locList');

        return locList;
      }
    } catch (e) {
      printLog('error-occured-11--${e.toString()}');
      // return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future autofillEntryExit(String id) async {
    try {
      String url = "$baseUrl/v1/fleets/$id";
      // printLog('getTruckListNew: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );

      if (response.statusCode == 200) {
        print('risaj----${response.body}');
        EntryExitTrailerAutofillModel locList =
            EntryExitTrailerAutofillModel.fromJson(json.decode(response.body));
        // print('risaj-spot---${locList}');

        return locList;
      }
    } catch (e) {
      printLog('error-occured-11--${e.toString()}');
      // return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future autofillCreateJob(String id) async {
    try {
      String url = "$baseUrl/v1/fleets/$id";
      // printLog('getTruckListNew: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );

      if (response.statusCode == 200) {
        print('risaj----${response.body}');
        CreateJobTrailerAutofillModel locList =
            CreateJobTrailerAutofillModel.fromJson(json.decode(response.body));
        // print('risaj-spot---${locList}');

        return locList;
      }
    } catch (e) {
      printLog('error-occured-11--${e.toString()}');
      // return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future getDriverListEntryExit(String pattern) async {
    try {
      SpotOnClient? spotOnClient = await Preferences.getSelectedClient();
      String url =
          "$baseUrl/v1/clients/${spotOnClient?.clientId}/users?isActive=true&page=0&size=10000&roleName=DRIVER&firstName=$pattern&sort=createdDate,desc";
      // printLog('getTruckListNew: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );

      if (response.statusCode == 200) {
        print('risaj----${response.body}');
        UsersListeEntryExit clientList =
            UsersListeEntryExit.fromJson(json.decode(response.body));
        print('risaj-spot---$clientList');

        return clientList;
      }
    } catch (e) {
      printLog('error-occured--33-${e.toString()}');
      // return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future getClientListEntryExit() async {
    try {
      SpotOnClient? spotOnClient = await Preferences.getSelectedClient();
      String url = "$baseUrl/v1/clients?isActive=true&page=0&size=1000";
      // printLog('getTruckListNew: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );

      if (response.statusCode == 200) {
        print('risaj----${response.body}');
        ClientListeEntryExit client =
            ClientListeEntryExit.fromJson(json.decode(response.body));
        print('risaj-spot---$client');

        return client;
      }
    } catch (e) {
      printLog('error-occured--55-${e.toString()}');
      // return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future getTrailerListEntryExit() async {
    try {
      SpotOnClient? spotOnClient = await Preferences.getSelectedClient();
      String url = "$baseUrl/v1/clients?isActive=true&page=0&size=1000";
      // printLog('getTruckListNew: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );

      if (response.statusCode == 200) {
        print('risaj----${response.body}');
        ClientListeEntryExit client =
            ClientListeEntryExit.fromJson(json.decode(response.body));
        print('risaj-spot---$client');

        return client;
      }
    } catch (e) {
      printLog('error-occured--44-${e.toString()}');
      // return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  Future sendFormData({
    required String billOfLanding,
    required String carrierId,
    required String clientSelectedId,
    required String dateOfArrival,
    required String dateOfPickup,
    required String driver,
    required String fleetId,
    required String loadStatus,
    required String locationId,
    required String proNumber,
    required String sequence,
    required String spotId,
    required String sub,
    required String supplier,
    required String tractorNumber,
    required String type,
    String? imagePath,
    required String notes,
// Make imagePath optional (nullable)
  }) async {
    print('value-----added--$imagePath');

    final url = Uri.parse(
        "$baseUrl/v1/clients/$clientSelectedId/locations/$locationId/fleetEntryExitNew");

    try {
      // Create a multipart request
      var request = MultipartRequest('POST', url);

      // Set the Authorization header
      request.headers['Authorization'] =
          "Bearer ${Preferences.loginResponseModel!.accessToken}";

      // Add form fields
      request.fields['billOfLandingType'] = billOfLanding;
      request.fields['carrier'] = carrierId;
      request.fields['clientId'] = clientSelectedId;
      request.fields['dateOfArrival'] = dateOfArrival;
      request.fields['dateOfPickup'] = dateOfPickup;
      request.fields['driver'] = driver;
      request.fields['fleetId'] = fleetId;
      request.fields['loadStatus'] = loadStatus;
      request.fields['locationId'] = locationId;
      request.fields['proNumber'] = proNumber;
      request.fields['sequenceNumber'] = sequence;
      request.fields['spotId'] = spotId;
      request.fields['sub'] = sub;
      request.fields['supplier'] = supplier;
      request.fields['tractorNumber'] = tractorNumber;
      request.fields['type'] = type;
      request.fields['notes'] = notes;

      // If imagePath is provided (not null or empty), add the image file
      if (imagePath != null) {
        print('value-----added');

        // Check if the file exists at the provided path
        File imageFile = File(imagePath);
        if (await imageFile.exists()) {
          request.files.add(await MultipartFile.fromPath(
            'billOfLandingImage',
            imagePath,
            filename: imagePath
                .split('/')
                .last, // Get the actual file name from the path
            contentType:
                MediaType('image', 'jpeg'), // Set to image/jpeg directly
          ));
        } else {
          throw Exception('File not found at the specified path: $imagePath');
        }
      }

      print('risaj----reqq---${request.fields.toString()}');
      // Send the request and get the response
      var response = await request.send();
      print('api---status-----${response.statusCode}');

      // Check the response
      if (response.statusCode == 204) {
        print('Form data submitted successfully');
      } else {
        throw Exception("Failed to submit form data: ${response.statusCode}");
      }
      return response.statusCode;
    } catch (e) {
      print('Error:---api---- ${e.toString()}');
    }
  }

  // Future<void> sendFormData({
  //   required String billOfLanding,
  //   required String carrierId,
  //   required String clientSelectedId,
  //   required String dateOfArrival,
  //   required String dateOfPickup,
  //   required String driver,
  //   required String fleetId,
  //   required String loadStatus,
  //   required String locationId,
  //   required String proNumber,
  //   required String sequence,
  //   required String spotId,
  //   required String sub,
  //   required String supplier,
  //   required String tractorNumber,
  //   required String type,
  //   required String imagePath,
  // }) async {
  //   final url = Uri.parse(
  //       "$baseUrl/v1/clients/$clientSelectedId/locations/$locationId/fleetEntryExit");

  //   try {
  //     // Create a multipart request
  //     var request = MultipartRequest('POST', url);

  //     // Set the Authorization header
  //     request.headers['Authorization'] =
  //         "Bearer ${Preferences.loginResponseModel!.accessToken}";

  //     // Add form fields
  //     request.fields['billOfLandingType'] = billOfLanding;
  //     request.fields['carrier'] = carrierId;
  //     request.fields['clientId'] = clientSelectedId;
  //     request.fields['dateOfArrival'] = dateOfArrival;
  //     request.fields['dateOfPickup'] = dateOfPickup;
  //     request.fields['driver'] = driver;
  //     request.fields['fleetId'] = fleetId;
  //     request.fields['loadStatus'] = loadStatus;
  //     request.fields['locationId'] = locationId;
  //     request.fields['proNumber'] = proNumber;
  //     request.fields['sequenceNumber'] = sequence;
  //     request.fields['spotId'] = spotId;
  //     request.fields['sub'] = sub;
  //     request.fields['supplier'] = supplier;
  //     request.fields['tractorNumber'] = tractorNumber;
  //     request.fields['type'] = type;

  //     // Add the binary image file directly with image MIME type
  //     var filePath = imagePath;

  //     request.files.add(await MultipartFile.fromPath(
  //       'billOfLandingImage',
  //       filePath,
  //       filename: 'basename($filePath)',
  //       contentType: MediaType('image', 'jpeg'), // Set to image/jpeg directly
  //     ));
  //     print('risaj----reqq---${request.fields.toString()}');
  //     // Send the request and get the response
  //     var response = await request.send();

  //     // Check the response
  //     if (response.statusCode == 204) {
  //       print('Form data submitted successfully');
  //     } else {
  //       throw Exception("sss");
  //       print('Failed to submit form data: ${response.statusCode}');
  //     }
  //   } catch (e) {
  //     //  throw Exception("sss");
  //     print('Error: ${e.toString()}');
  //   }
  // }

  /////////////

  static Future getEntryExitReportList() async {
    try {
      String url =
          "$baseUrl/v1/fleets/entryExits?isActive=true&page=0&size=10&sort=createdDate,desc";
      // printLog('getEntryExitReportList: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        EntryExitReportListModel entryExitReportListModel =
            entryExitReportListModelFromJson(response.body);
        return Success(
            code: response.statusCode, response: entryExitReportListModel);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future createJob(JobRequestModel jobRequestModel) async {
    try {
      String url = "$baseUrl/v1/jobs";
      // printLog('createJob: $url');
      // printLog(jobRequestModelToJson(jobRequestModel));
      print(
          'object-----${jobRequestModel.assignedToUserId}--77--${jobRequestModel.description}---1--${jobRequestModel.dropLocationId}---1--${jobRequestModel.fleetId}--1---${jobRequestModel.pickupLocationId}---1--${jobRequestModel.pickupSpotId}--1---${jobRequestModel.assignedToUserId}---1--${jobRequestModel.dropSpotId}---1--${jobRequestModel.priority}----1--${jobRequestModel.fleetStatus}---9--');
      printLog(jobRequestModel.toJson());
      Response response = await post(
        Uri.parse(url),
        body: jobRequestModelToJson(jobRequestModel),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 201) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      printLog('---error----${e.toString()}');
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getUserInfo({required String userId}) async {
    try {
      String url = "$baseUrl/v1/users/$userId";
      // printLog('getUserInfo: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == 200) {
        UserInfoModel userInfoModel = userInfoModelFromJson(response.body);
        return Success(code: response.statusCode, response: userInfoModel);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getHomeJobs() async {
    try {
      String url =
          "$baseUrl/v1/jobs?status=OPEN&status=IN_TRANSIT&sort=status,createdDate";
      if (isDriver() || isSpotter()) {
        url = "$baseUrl/v1/jobs/mine";
      }
      // printLog('getHomeJobs: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        JobsListModel jobsListModel = jobsListModelFromJson(response.body);
        return Success(code: response.statusCode, response: jobsListModel);
      }
      return _getFailure(response);
    } catch (e) {
      printLog('jino1');
      showToast(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getJobs({required String filter}) async {
    // String? deviceUuid = await PlatformDeviceId.getDeviceId;
    String? uid = Preferences.loginResponseModel!.userId;

    try {
      String url = isClient()
          ? "$baseUrl/v1/jobs?$filter&status=IN_TRANSIT&status=OPEN&createdBy=$uid&size=500&sort=lastModifiedDate,DESC"
          : "$baseUrl/v1/jobs?$filter&size=500&sort=lastModifiedDate,DESC";
      // printLog('getJobs: $url');
      log("BEARER ${Preferences.loginResponseModel!.accessToken}");
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog();
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        JobsListModel jobsListModel = jobsListModelFromJson(response.body);
        // print(url);
        // print(jobsListModel.list?.length);
        return Success(code: response.statusCode, response: jobsListModel);
      }
      return _getFailure(response);
    } catch (e) {
      printLog('jinooo-----${e.toString()}');
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getCarriers() async {
    try {
      String url = "$baseUrl/v1/carrier?isActive=true&page=0&size=1000";
      // printLog('getJobs: $url');
      log("BEARER ${Preferences.loginResponseModel!.accessToken}");
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        List<String> carrierList = carrierListFromJson(response.body);

        // print(carrierList);
        return Success(code: response.statusCode, response: carrierList);
      }
      return _getFailure(response);
    } catch (e) {
      printLog('jino11');

      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future doJobUpdate({
    required String jobId,
    required String jobUpdateStatus,
    String? lat,
    String? lng,
    String notes = '',
    String? statusDateTime,
  }) async {
    try {
      String url = "$baseUrl/v1/jobs/$jobId/status";
      // printLog('doJobUpdate: $url');
      Response response = await patch(
        Uri.parse(url),
        body: json.encode({
          "notes": notes,
          "status": jobUpdateStatus,
          "lat": lat,
          "lng": lng,
        }),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 204) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future doJobUpdateWithDropSpotId({
    required String jobId,
    required ConfirmDropUpdateDropModel confirmDropUpdateDropModel,
    String notes = '',
    String? statusDateTime,
  }) async {
    try {
      String url = "$baseUrl/v1/jobs/$jobId";
      // printLog('doJobUpdateWithDropSpotId: $url');
      Response response = await put(
        Uri.parse(url),
        body: confirmDropUpdateDropModelToJson(confirmDropUpdateDropModel),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 204) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      printLog('jino13');

      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future updateProfile(ProfileUpdateModel profileUpdateModel) async {
    try {
      String url = "$baseUrl/v1/profile";
      // printLog('updateProfile: $url');
      // printLog(profileUpdateModelToJson(profileUpdateModel));
      Response response = await put(
        Uri.parse(url),
        body: profileUpdateModelToJson(profileUpdateModel),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog('${response.statusCode}');
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 204) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future doFleetEntryExit(
    String fleetId,
    String notes,
    String type,
    String locationId,
  ) async {
    try {
      SpotOnClient? spotOnClient = await Preferences.getSelectedClient();
      if (null == spotOnClient) {
        return Failure(
            code: -1, response: 'Error occurred.\nPlease try again.');
      }
      String url =
          "$baseUrl/v1/clients/${spotOnClient.clientId}/locations/$locationId/fleetEntryExit";
      // printLog('doFleetEntryExit: $url');
      Response response = await post(
        Uri.parse(url),
        body: jsonEncode({"fleetId": fleetId, "notes": notes, "type": type}),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 204) {
        return Success(code: response.statusCode, response: true);
      }
      print('failed--catch-1111299000-');

      return _getFailure(response);
    } catch (e) {
      print('failed--catch-111129900088-');

      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future resetPassword(String email) async {
    try {
      String url = "$baseUrl/v1/profile/forgotPassword";
      // printLog('resetPassword: $url');
      Response response = await put(
        Uri.parse(url),
        body: jsonEncode({"email": email}),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
        },
      );
      // printLog('${response.statusCode}');
      if (response.statusCode == 204) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future resetPassword2(
      String password, String confirmPassword, String token) async {
    try {
      String url = "$baseUrl/v1/profile/resetPassword";
      // printLog('resetPassword2: $url');
      Response response = await put(
        Uri.parse(url),
        body: jsonEncode({
          "confirmNewPassword": confirmPassword,
          "newPassword": password,
          "token": token,
        }),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
        },
      );
      // printLog(response.body);
      if (response.statusCode == 204) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future addTruck(NewTrailerReqModel newTrailerReqModel) async {
    try {
      String url = "$baseUrl/v1/fleets";
      // printLog('addTruck: $url');
      SpotOnClient? client = await Preferences.getSelectedClient();
      newTrailerReqModel.clientIds.clear();
      newTrailerReqModel.clientIds.add(client!.clientId!);
      Response response = await post(
        Uri.parse(url),
        body: newTrailerReqModelToJson(newTrailerReqModel),
        headers: {
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}",
          HttpHeaders.contentTypeHeader: 'application/json',
        },
      );
      // printLog(response.body);
      if (response.statusCode == 200 || response.statusCode == 201) {
        return Success(code: response.statusCode, response: true);
      }
      print('failed--catch-1111299-');

      return _getFailure(response);
    } catch (e) {
      print('failed--catch-11112-');

      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future registerDevice({
    required String deviceModel,
    required String deviceName,
    required String deviceRegistrationId,
    required String deviceType,
  }) async {
    try {
      log("BEARER ${Preferences.loginResponseModel!.accessToken}");
      String? deviceUuid = await PlatformDeviceId.getDeviceId;

      String url = "$baseUrl/v1/users/devices";
      // printLog('registerDevice: $url');
      Response response = await post(
        Uri.parse(url),
        body: jsonEncode({
          "deviceModel": deviceModel,
          "deviceName": deviceName,
          "deviceRegistrationId": deviceRegistrationId,
          "deviceType": deviceType,
          "deviceUuid": deviceUuid,
        }),
        headers: {
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}",
          HttpHeaders.contentTypeHeader: 'application/json',
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future registerDeviceNew({
    required String deviceModel,
    required String deviceName,
    required String deviceRegistrationId,
    required String deviceType,
  }) async {
    try {
      String? deviceUuid = await PlatformDeviceId.getDeviceId;
      print('?//////////////////---$deviceUuid');
      log("BEARER ${Preferences.loginResponseModel!.accessToken}");
      String url =
          "$baseUrl/v1/deviceregister/$deviceUuid/$deviceRegistrationId";
      printLog('registerDevice: $url');
      Response response = await post(
        Uri.parse(url),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      print('failed--catch-1111-');

      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future postTrailerAudit({
    required PostTrailerAudit postTrailerAudit,
  }) async {
    printLog(
        '----posttrailer-----${postTrailerAudit.area}---1----${postTrailerAudit.carrier}--2---${postTrailerAudit.fleetId}---3--${postTrailerAudit.locationId}--4---${postTrailerAudit.notes}---5--${postTrailerAudit.slot}---6----${postTrailerAudit.trailerStatus}------');

    try {
      log("BEARER ${Preferences.loginResponseModel!.accessToken}");
      String url = "$baseUrl/v1/trailerAudit";
      // printLog('registerDevice: $url');
      Response response = await post(
        Uri.parse(url),
        body: postTrailerAuditToJson(postTrailerAudit),
        headers: {
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}",
          HttpHeaders.contentTypeHeader: 'application/json',
        },
      );
      printLog('----post-----${response.statusCode}');
      printLog('----post-----${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      print('failed--catch-11-');

      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getTrailerAudit() async {
    try {
      String url = "$baseUrl/v1/trailerAudit?size=5000";
      printLog('getJobs: $url');
      log("BEARER ${Preferences.loginResponseModel!.accessToken}");
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      printLog('---status-------${response.body}');
      printLog('status: ${response.statusCode}');

      if (response.statusCode == unauthorized) {
        print('failure--unauth--');

        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        // print('pass--unauth-failed-');

        printLog(response.body);
        var trailerAudit = getTrailerAuditFromJson(response.body);
        // print('--------${traile}');

        // print(carrierList);
        return Success(code: response.statusCode, response: trailerAudit);
      }
      print('failed--unauth--');

      return _getFailure(response);
    } catch (e) {
      print('failed--catch--');

      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  // Bulk save trailer audit (POST with array body)
  static Future postBulkTrailerAudit({
    required List<BulkTrailerAuditItem> trailerAuditList,
  }) async {
    try {
      String url = "$baseUrl/v1/trailerAudit";
      printLog('postBulkTrailerAudit: $url');
      printLog('Body: ${bulkTrailerAuditToJson(trailerAuditList)}');

      Response response = await post(
        Uri.parse(url),
        body: bulkTrailerAuditToJson(trailerAuditList),
        headers: {
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}",
          HttpHeaders.contentTypeHeader: 'application/json',
        },
      );

      printLog('Bulk trailer audit response status: ${response.statusCode}');
      printLog('Bulk trailer audit response body: ${response.body}');

      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200 || response.statusCode == 201) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      printLog('Bulk trailer audit error: ${e.toString()}');
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  // Clear individual trailer audit (PATCH with spotId path variable)
  static Future clearTrailerAudit({
    required String spotId,
  }) async {
    try {
      String url = "$baseUrl/v1/trailerAudit/$spotId";
      printLog('clearTrailerAudit: $url');

      Response response = await patch(
        Uri.parse(url),
        headers: {
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}",
          HttpHeaders.contentTypeHeader: 'application/json',
        },
      );

      printLog('Clear trailer audit response status: ${response.statusCode}');
      printLog('Clear trailer audit response body: ${response.body}');

      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200 || response.statusCode == 204) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      printLog('Clear trailer audit error: ${e.toString()}');
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  // Get in-transit jobs to check for fleet IDs in transit
  static Future getInTransitJobs() async {
    try {
      String url =
          "$baseUrl/v1/jobs?isActive=true&page=0&size=1000&sort=createdDate,desc&status=IN_TRANSIT";
      printLog('getInTransitJobs: $url');

      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );

      printLog('In-transit jobs response status: ${response.statusCode}');

      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        // Return the raw response as a Map to be processed by the caller
        Map<String, dynamic> responseData = json.decode(response.body);
        return Success(code: response.statusCode, response: responseData);
      }
      return _getFailure(response);
    } catch (e) {
      printLog('Get In-Transit Jobs Error: $e');
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getAllInTransitJobs() async {
    try {
      String url =
          "$baseUrl/v1/jobs?isActive=true&page=0&size=1000&sort=createdDate,desc&status=IN_TRANSIT&allClientJobs=true";
      printLog('getInTransitJobs: $url');

      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );

      printLog('In-transit jobs response status: ${response.statusCode}');

      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        // Return the raw response as a Map to be processed by the caller
        Map<String, dynamic> responseData = json.decode(response.body);
        return Success(code: response.statusCode, response: responseData);
      }
      return _getFailure(response);
    } catch (e) {
      printLog('Get In-Transit Jobs Error: $e');
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future postDamageReport({
    required DamageReportModel damageReport,
  }) async {
    printLog('data--------${damageReport.toJson()}');
    printLog(
        '----post-damage-----${damageReport.comments}---1----${damageReport.message}-2----${damageReport.fleetId}--');

    try {
      log("BEARER ${Preferences.loginResponseModel!.accessToken}");
      String url =
          "$baseUrl/v1/fleets/fleet/damaged/true?fleetId=${damageReport.fleetId}&message=${damageReport.message}&comments=${damageReport.comments}";
      Response response = await put(
        Uri.parse(url),
        // body: damageReportToJson(damageReport),
        headers: {
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}",
          HttpHeaders.contentTypeHeader: 'application/json',
        },
      );
      printLog('----post-----${response.statusCode}');
      printLog('----post-----${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return Success(code: response.statusCode, response: true);
      }
      return _getFailure(response);
    } catch (e) {
      print('failed--catch-11-');

      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }

  static Future getClientDetails(String clientId) async {
    try {
      String url = "$baseUrl/v1/clients/$clientId";
      // printLog('getSpots: $url');
      Response response = await get(
        Uri.parse(url),
        headers: {
          HttpHeaders.contentTypeHeader: 'application/json',
          "Authorization":
              "BEARER ${Preferences.loginResponseModel!.accessToken}"
        },
      );
      // printLog(response.body);
      if (response.statusCode == unauthorized) {
        return Failure(code: unauthorized, response: 'Unauthorized');
      }
      if (response.statusCode == 200) {
        ClientDetailsNewModel clientDetailsNewModel =
            clientDetailsNewModelFromJson(response.body);
        return Success(
            code: response.statusCode, response: clientDetailsNewModel);
      }
      return _getFailure(response);
    } catch (e) {
      // printLog(e.toString());
      return Failure(code: -1, response: 'Error occurred.\nPlease try again.');
    }
  }
}
