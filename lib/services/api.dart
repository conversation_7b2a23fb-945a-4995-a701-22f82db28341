import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:image_picker/image_picker.dart';
import 'package:spot_on/models/bol/bol_dvir.dart';
import 'package:spot_on/utils/imports.dart';

import '../utils/app_logger.dart';

class DioClient {
  static final DioClient _singleton = DioClient._internal();

  factory DioClient() {
    return _singleton;
  }

  DioClient._internal();

  Dio? _dio;

  Dio getInstance() {
    // if (_dio != null) {
    //   return _dio!;
    // } else {
    _dio = _getDio();
    return _dio!;
    // }
  }

  Dio _getDio() {
    var options = BaseOptions(
      baseUrl: Services.baseUrl,
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60),
    );
    var dio = Dio(options);
    dio.options.headers["Authorization"] =
        "BEARER ${Preferences.loginResponseModel?.accessToken}";
    dio
      ..interceptors.clear()
      ..interceptors.addAll(
        [
          RetryInterceptor(
            dio: dio,
            logPrint: printLog,
            retries: 1,
            retryDelays: const [Duration(seconds: 2)],
          )
        ],
      );
    return dio;
  }
}

class Api {
  var dio = DioClient().getInstance();

  Future<dynamic> uploadBol({
    required List<XFile> images,
    required String jobId,
    required bool isUnsigned,
  }) async {
    try {
      FormData formData = FormData();
      formData.fields.add(MapEntry('jobId', jobId));
      formData.fields.add(MapEntry('type', isUnsigned ? "unsigned" : "signed"));
      for (var i in images) {
        formData.files.add(
          MapEntry(
            'files',
            await MultipartFile.fromFile(
              File(i.path).path,
              filename: i.name,
              // contentType: MediaType('image', 'png'),
            ),
          ),
        );
      }

      dio.options.headers["content-type"] = "multipart/form-data";
      Response response = await dio.post(
        "/v1/bol",
        data: formData,
      );
      return "Success";
    } on DioException catch (e) {
      if (e.response?.statusCode == unauthorized) {
        openLogin();
      }
      return "Failure";
    }
  }

  Future<dynamic> getBolDvirStatus() async {
    try {
      Response response = await dio.get("/v1/clients/config");
      var data = response.data;
      print('----reponse----$data');
      return BolDvir.fromJson(data);
    } catch (e) {
      rethrow;
    }
  }

  Future<dynamic> uploadTrailerStand({
    required List<XFile> images,
    required String jobId,
    required String reason,
  }) async {
    try {
      FormData formData = FormData();
      formData.fields.add(MapEntry('jobId', jobId));
      formData.fields.add(MapEntry('reason', reason));
      for (var i in images) {
        formData.files.add(
          MapEntry(
            'files',
            await MultipartFile.fromFile(
              File(i.path).path,
              filename: i.name,
              // contentType: MediaType('image', 'png'),
            ),
          ),
        );
      }

      dio.options.headers["content-type"] = "multipart/form-data";
      Response response = await dio.post(
        "/v1/trailerStandPhoto",
        data: formData,
      );
      return "Success";
    } on DioException catch (e) {
      if (e.response?.statusCode == unauthorized) {
        openLogin();
      }
      return "Failure";
    }
  }
}
