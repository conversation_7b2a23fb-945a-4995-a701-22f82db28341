import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:spot_on/models/client_listentryexit.dart';
import 'package:spot_on/models/driverlistentryexit.dart';
import 'package:spot_on/models/location_listentryexit.dart';
import 'package:spot_on/models/spot_list_entryexit.dart';
import 'package:spot_on/models/supplierlist.dart';
import 'package:spot_on/services/services.dart';

import '../../../models/autopopulateexit.dart';
import '../../../models/truck_list_model.dart';

// import '../../models/truck_list_model.dart';

part 'entry_exit_state.dart';

class EntryExitCubit extends Cubit<EntryExitState> {
  EntryExitCubit() : super(EntryExitInitial());

  Future<void> getCarrierEntry() async {
    emit(EntryExitInitial());

    try {
      var body = await Services().getCarrierListEntryExit();

// alertmsg('${AppPref.getString('msg')}');

      emit(SuccessCarrier(carrierdetails: body));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-555-$e');
    }
  }

  Future<void> getSupplierEntry() async {
    emit(EntryExitInitial());

    try {
      var body = await Services().getSupplierListEntryExit();

// alertmsg('${AppPref.getString('msg')}');

      emit(SuccessSupplier(supplierdetails: body));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-444-$e');
    }
  }

  Future<void> createCarrierEntry(String text) async {
    emit(EntryExitInitial());

    try {
      var body = await Services().addCarrierListEntryExit(text);

// alertmsg('${AppPref.getString('msg')}');

      emit(SuccessCreate(msgs: body.toString()));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-333-$e');
    }
  }

  Future<void> createSupplierEntry(String text) async {
    emit(EntryExitInitial());

    try {
      var body = await Services().addSupplierListEntryExit(text);

// alertmsg('${AppPref.getString('msg')}');

      emit(SuccessCreate(msgs: body.toString()));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-2222-$e');
    }
  }

  Future<void> getSpotEntryExit(String loc) async {
    emit(EntryExitInitial());

    try {
      SpotListeEntryExit body = await Services().getSpotListEntryExit(loc);

// alertmsg('${AppPref.getString('msg')}');
      print('risajj-----${body.page}.');
      emit(SuccessSpot(spotdetails: body));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-cubit-111----$e');
    }
  }

  Future<void> getLocationEntryExit() async {
    emit(EntryExitInitial());

    try {
      LocationListeEntryExit body = await Services().getLocationListEntryExit();

// alertmsg('${AppPref.getString('msg')}');
      print('risajj-----${body.page}.');
      emit(SuccessLocation(locdetails: body));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-cubit-9999----$e');
    }
  }

  Future<void> autofillSpot(String id) async {
    emit(EntryExitInitial());

    try {
      EntryExitTrailerAutofillModel body =
          await Services().autofillEntryExit(id);

// alertmsg('${AppPref.getString('msg')}');
      print('risajj-----$body.');
      emit(SuccessAutofill(list: body));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-cubit-9999222----$e');
    }
  }

  Future<void> getDriverEntryExit() async {
    emit(EntryExitInitial());

    try {
      UsersListeEntryExit body = await Services().getDriverListEntryExit('');

// alertmsg('${AppPref.getString('msg')}');
      print('risajj-----${body.page}.');
      emit(SuccessDriver(driverdetails: body));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-cubit000-$e');
    }
  }

  Future<void> getClientEntryExit() async {
    emit(EntryExitInitial());

    try {
      ClientListeEntryExit body = await Services().getClientListEntryExit();

// alertmsg('${AppPref.getString('msg')}');
      print('risajj-----${body.page}.');
      emit(SuccessClient(clientdetails: body));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-cubit-$e');
    }
  }

  Future<void> getTrailerEntryExit() async {
    emit(EntryExitInitial());

    try {
      ClientListeEntryExit body = await Services().getTrailerListEntryExit();

// alertmsg('${AppPref.getString('msg')}');
      print('risajj-----${body.page}.');
      // emit(SuccessTrailer(driverdetails:   body));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-cubit-ppp--$e');
    }
  }

  Future<void> newTrailerAddedEntryExit(List<TruckDetail> list) async {
    // emit(EntryExitInitial());

    try {
      emit(SuccessCreateTrailer(list: list));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-cubit-ppp--$e');
    }
  }

  Future<void> submit({
    required String bill,
    required String carrierName,
    required String clientId,
    required String arrivalDate,
    required String datePickup,
    required String fleetId,
    required String loadStatus,
    required String location,
    required String proNumber,
    required String driverId,
    required String sequence,
    required String spot,
    required String sub,
    required String supplierName,
    required String tractorNumber,
    required String selectedOption,
    required String? img,
    required String notes,
  }) async {
    emit(EntryExitInitial());

    try {
      var body = await Services().sendFormData(
        billOfLanding: bill,
        carrierId: carrierName,
        clientSelectedId: clientId,
        dateOfArrival: arrivalDate,
        dateOfPickup: datePickup,
        fleetId: fleetId,
        loadStatus: loadStatus,
        locationId: location,
        proNumber: proNumber,
        driver: driverId,
        sequence: sequence,
        spotId: spot,
        sub: sub,
        supplier: supplierName,
        tractorNumber: tractorNumber,
        type: selectedOption,
        imagePath: img,
        notes: notes,
      );

// alertmsg('${AppPref.getString('msg')}');
// print('risajj-----${body.page}.');
// if()
      if (body == 204) {
        emit(const SuccessSubmit(msgs: ''));
      } else {
        emit(const FailedSubmit(msgs: 'cannot deactivate this fleet !'));
      }
    } catch (e) {
      emit(const FailedSubmit(msgs: 'cannot deactivate this fleet !'));

      emit(Message(msg: e.toString()));
      print('-1-1-1-1-cubit-898--$e');
    }
  }
}
