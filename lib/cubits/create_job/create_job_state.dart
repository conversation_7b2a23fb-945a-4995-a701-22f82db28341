part of 'create_job_cubit.dart';

class CreateJobTrailerState extends Equatable {
  const CreateJobTrailerState();

  @override
  List<Object> get props => [];
}

class CreateJobInitial extends CreateJobTrailerState {}

class SuccessAutofill extends CreateJobTrailerState {
  final CreateJobTrailerAutofillModel list;
  const SuccessAutofill({required this.list});
  @override
  List<Object> get props => [];
}

class Message extends CreateJobTrailerState {
  final String? msg;

  const Message({this.msg});
  @override
  List<Object> get props => [];
}

class SuccessSpot extends CreateJobTrailerState {
  final CreateJobSpotList? createJobSpotList;

  const SuccessSpot({required this.createJobSpotList});
  @override
  List<Object> get props => [];
}
