import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../models/createjob_autofill.dart';
import '../../models/createjob_list.dart';
import '../../services/services.dart';

part 'create_job_state.dart';

class CreateJobTrailerCubit extends Cubit<CreateJobTrailerState> {
  CreateJobTrailerCubit() : super(CreateJobInitial());

  Future<void> autofillSpot(String id) async {
    emit(CreateJobInitial());

    try {
      CreateJobTrailerAutofillModel body =
          await Services().autofillCreateJob(id);

      // alertmsg('${AppPref.getString('msg')}');
      print('risajj-----$body.');
      emit(SuccessAutofill(list: body));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-cubit-9999222----$e');
    }
  }

  Future<void> getSpotCreateJob(String loc) async {
    emit(CreateJobInitial());

    try {
      CreateJobSpotList body = await Services().getSpotListCreateJob(loc);

// alertmsg('${AppPref.getString('msg')}');
      print('risajj-----${body.page}.');
      emit(SuccessSpot(createJobSpotList: body));
    } catch (e) {
      emit(Message(msg: e.toString()));
      print('-1-1-1-1-cubit-111----$e');
    }
  }
}
