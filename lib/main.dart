import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:material_color_gen/material_color_gen.dart';
import 'package:provider/provider.dart';
import 'package:spot_on/cubits/create_job/create_job_cubit.dart';
import 'package:spot_on/providers/bol_upload_state.dart';
import 'package:spot_on/providers/create_job_state.dart';
import 'package:spot_on/providers/home_state.dart';
import 'package:spot_on/providers/jobs_state.dart';
import 'package:spot_on/providers/login_state.dart';
import 'package:spot_on/providers/new_trailer_state.dart';
import 'package:spot_on/providers/trailer_audit_state.dart';
import 'package:spot_on/screens/splash_screen.dart';
import 'package:spot_on/utils/constants.dart';
import 'package:spot_on/utils/push_notifications.dart';
import 'package:spot_on/widgets/global_widget.dart';
import 'cubits/entry_exit/cubit/entry_exit_cubit.dart';
import 'firebase_options.dart';
import 'providers/damage_report_state.dart';
import 'providers/trailer_stand_upload_state.dart';

final GlobalKey<ScaffoldMessengerState> snackBarKey =
    GlobalKey<ScaffoldMessengerState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    name: "spot-on-fb",
    options: DefaultFirebaseOptions.currentPlatform,
  );
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  runApp(const MyApp());
}

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  firebaseMessageBackgroundHandler();
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var color = const Color(0XFF56FFB5).toMaterialColor();
    return MultiProvider(
      providers: [
        BlocProvider(create: (context) => EntryExitCubit()),
        ChangeNotifierProvider(create: (_) => LoginState()),
        ChangeNotifierProvider(create: (_) => HomeState()),
        ChangeNotifierProvider(create: (_) => CreateJobState()),
        ChangeNotifierProvider(create: (_) => JobsState()),
        ChangeNotifierProvider(create: (_) => NewTrailerState()),
        ChangeNotifierProvider(create: (_) => BolUploadState()),
        ChangeNotifierProvider(create: (_) => TrailerAuditState()),
        ChangeNotifierProvider(create: (_) => DamageReportState()),
        ChangeNotifierProvider(create: (_) => TrailerStandUploadState()),
        BlocProvider(create: (context) => CreateJobTrailerCubit()),
      ],
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        title: appName,
        navigatorKey: globalKey,
        scaffoldMessengerKey: snackBarKey,
        theme: ThemeData(
          primarySwatch: color,
          useMaterial3: false,
          primaryColor: color,
          fontFamily: fontBronova,
          progressIndicatorTheme:
              const ProgressIndicatorThemeData(color: appBg),
          radioTheme:
              RadioThemeData(fillColor: WidgetStateProperty.all(appBg)),
        ),
        home: const GlobalWidget(child: SplashScreen()),
      ),
    );
  }
}
