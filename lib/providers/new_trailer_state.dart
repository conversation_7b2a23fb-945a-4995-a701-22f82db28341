import 'package:spot_on/models/cliient_list_model.dart';
import 'package:spot_on/models/new_trailer_req_model.dart';
import 'package:spot_on/models/truck_list_model.dart';
import 'package:spot_on/providers/create_job_state.dart';
import 'package:spot_on/providers/trailer_audit_state.dart';
import 'package:spot_on/utils/imports.dart';

import '../cubits/entry_exit/cubit/entry_exit_cubit.dart';

class NewTrailerState extends ChangeNotifier {
  NewTrailerReqModel newTrailerReqModel = NewTrailerReqModel(
    carrier: '',
    owner: 'A <PERSON>',
    remarks: '',
    type: '',
    unitNumber: '',
    isHotTrailer: true,
    clientIds: [],
  );

  ClientListModel clientListModel = ClientListModel();
  List<String> carrierList = [];

  List<TrucKType> typesList = [
    TrucKType(
      id: 0,
      displayValue: 'Truck',
      value: 'TRUCK',
    ),
    TrucKType(
      id: 1,
      displayValue: 'Trailer',
      value: 'TRAILER',
    ),
    TrucKType(
      id: 2,
      displayValue: 'Container',
      value: 'CONTAINER',
    )
  ];

  TrucKType? selectedTruckType;
  bool addingTruck = false;
  bool newTruckAdded = false;
  SpotOnClient? selectedClient;

  loadClients() async {
    var client = await Preferences.getSelectedClient();
    print(client?.clientName);
    var result = await Services.getClients();
    if (result is Success) {
      clientListModel = result.response as ClientListModel;
      var client = await Preferences.getSelectedClient();
      print(client?.clientName);
      if (client != null) {
        selectedClient = clientListModel.list.firstWhere(
            (element) => element.clientId == client.clientId, orElse: () {
          return selectedClient = clientListModel.list.first;
        });
      } else {
        selectedClient = clientListModel.list.first;
      }
      notifyListeners();
    } else {
      String errorMessage = result.response as String;
      showSnackBar(errorMessage);
    }
  }

  loadCarriers() async {
    var result = await Services.getCarriers();
    if (result is Success) {
      carrierList = result.response as List<String>;
      print('jno---$carrierList');
      notifyListeners();
    } else {
      String errorMessage = result.response as String;
      showSnackBar(errorMessage);
    }
  }

  refresh() async {
    notifyListeners();
  }

  setAddingTruck(bool loading) async {
    addingTruck = loading;
    notifyListeners();
  }

  setSelectedClient(SpotOnClient selectedClient) {
    selectedClient = selectedClient;
    notifyListeners();
  }

  resetState() {
    selectedClient = null;
    newTrailerReqModel = NewTrailerReqModel(
      carrier: '',
      owner: 'A Blair',
      remarks: '',
      type: '',
      unitNumber: '',
      isHotTrailer: true,
      clientIds: [],
    );
    newTruckAdded = false;
    carrierList = [];
  }

  setDefaultTrailerType() {
    selectedTruckType = typesList.firstWhere((element) => element.id == 1);
    newTrailerReqModel.type = selectedTruckType!.value;
  }

  isValid() {
    if (newTrailerReqModel.unitNumber.trim().isEmpty) {
      return false;
    }
    return true;
  }

  addTruck() async {
    setAddingTruck(true);
    newTrailerReqModel.clientIds.clear();
    newTrailerReqModel.clientIds.add(selectedClient!.clientId!);
    var result = await Services.addTruck(newTrailerReqModel);
    if (result is Success) {
      showSnackBar('Trailer added successfully');
      newTruckAdded = true;
      closeScreen();
      CreateJobState createJobState =
          globalKey.currentContext!.read<CreateJobState>();
      TrailerAuditState trailerAuditState =
          globalKey.currentContext!.read<TrailerAuditState>();
      // createJobState.getTruckList();

      var result =
          await Services.getTruckListNew(newTrailerReqModel.unitNumber);
      if (result is Success) {
        var truckList = result.response as TruckListModel;
        createJobState.autoCompleteController.text =
            truckList.list.first.unitNumber ?? '';
        createJobState.selectedTruckDetail = truckList.list.first;
        createJobState.jobRequestModel.fleetId =
            truckList.list.first.fleetId ?? '';
        trailerAuditState.autoCompleteController.text =
            truckList.list.first.unitNumber ?? '';
        trailerAuditState.selectedTruckDetail = truckList.list.first;
        trailerAuditState.carrier = truckList.list.first.carrier;
        globalKey.currentContext
            ?.read<EntryExitCubit>()
            .newTrailerAddedEntryExit(truckList.list);
        trailerAuditState.refresh();
        notifyListeners();
      }
    }
    if (result is Failure) {
      newTruckAdded = false;
      String errorMessage = result.response as String;
      showSnackBar(
        errorMessage,
        success: false,
      );
    }
    setAddingTruck(false);
  }
}

class TrucKType {
  int id;
  String displayValue;
  String value;

  TrucKType({
    required this.id,
    required this.displayValue,
    required this.value,
  });
}
