import 'package:autocomplete_textfield/autocomplete_textfield.dart';
import 'package:spot_on/models/traileraudit/get_trailer_audit.dart';
import 'package:spot_on/models/traileraudit/post_trailer_audit.dart';
import 'package:spot_on/models/traileraudit/bulk_trailer_audit.dart';
import 'package:spot_on/models/truck_list_model.dart';
import 'package:spot_on/models/new_trailer_req_model.dart';
import 'package:spot_on/utils/imports.dart';

import '../models/cliient_list_model.dart';
import '../models/spot_on_locations.dart';
import '../models/spots_list.dart';
import '../utils/app_logger.dart';
import 'create_job_state.dart';

// Class to represent individual dock entries
class DockEntry {
  final Spot spot;
  TruckDetail? selectedTruckDetail;
  FleetStatus? selectedFleetStatus;
  String? notes;
  ListElement? existingAudit; // Reference to existing trailer audit data
  late TextEditingController notesController;
  late TextEditingController trailerController; // Controller for trailer search field
  bool isEdited = false; // Flag to track if any field has been edited
  bool isTransit = false; // Flag to track if trailer is in transit
  int rebuildCounter =
      0; // Counter to force UI rebuilds when values are restored

  // Original values from spot data when location is selected (baseline)
  String? originalFleetId; // From spot.fleet.fleetId
  String? originalFleetStatus; // From spot.fleet.fleetStatus
  String? originalNotes; // From spot.fleet.remarks or spot.remarks
  String? originalUnitNumber; // From spot.fleet.unitNumber

  DockEntry({
    required this.spot,
    this.selectedTruckDetail,
    this.selectedFleetStatus,
    this.notes,
    this.existingAudit,
    this.isEdited = false, // Initialize to false by default
    this.isTransit = false, // Initialize to false by default
  }) {
    notesController = TextEditingController(text: notes ?? '');
    trailerController = TextEditingController(text: selectedTruckDetail?.unitNumber ?? '');
    // Store original values from spot data as baseline
    _storeOriginalSpotData();
  }

  // Store original values from spot data
  void _storeOriginalSpotData() {
    if (spot.fleet != null) {
      originalFleetId = spot.fleet!.fleetId;
      originalFleetStatus = spot.fleet!.fleetStatus;
      originalUnitNumber = spot.fleet!.unitNumber;

      // Use notes field from API response, fallback to fleet remarks
      originalNotes = spot.notes; // Use notes field from API response
      if (originalNotes == null || originalNotes!.isEmpty) {
        originalNotes = spot.fleet!.remarks; // Fallback to fleet remarks
      }
    } else {
      originalFleetId = null;
      originalFleetStatus = null;
      originalUnitNumber = null;
      originalNotes =
          spot.notes; // Use notes field from API response when no fleet data
    }

    printLog(
        'Stored original data for ${spot.spotName}: FleetId=$originalFleetId, Status=$originalFleetStatus, UnitNumber=$originalUnitNumber, Notes="$originalNotes" (Source: ${spot.notes != null && spot.notes!.isNotEmpty ? "spot notes" : "fleet remarks"})');
  }

  void clear() {
    selectedTruckDetail = null;
    selectedFleetStatus = null;
    notes = null;
    notesController.clear();
    trailerController.clear();
    isEdited = false; // Reset the edit flag when cleared
  }

  void updateNotes(String newNotes) {
    notes = newNotes;
    if (notesController.text != newNotes) {
      notesController.text = newNotes;
    }
  }

  // Check if current form values are different from original spot data
  bool hasChanges() {
    // Get current form values
    String currentStatus = selectedFleetStatus?.id ?? '';
    String currentUnitNumber = selectedTruckDetail?.unitNumber ?? '';
    String currentNotes = notes ?? '';

    // Get original values from spot data
    String originalStatus = originalFleetStatus ?? '';
    String originalUnit = originalUnitNumber ?? '';
    String originalNote = originalNotes ?? '';

    // Compare the key fields: status, unitNumber, notes
    bool statusChanged = currentStatus != originalStatus;
    bool unitNumberChanged = currentUnitNumber != originalUnit;
    bool notesChanged = currentNotes != originalNote;

    bool hasChanges = statusChanged || unitNumberChanged || notesChanged;

    return hasChanges;
  }

  // Check if all required fields are filled for this entry
  bool hasAllRequiredFields() {
    return selectedTruckDetail != null && selectedFleetStatus != null;
  }

  // Check if this entry is valid for saving (either complete data or notes-only changes)
  bool isValidForSaving() {
    // Case 1: Complete entry with trailer and status
    if (selectedTruckDetail != null && selectedFleetStatus != null) {
      return true;
    }

    // Case 2: Notes-only changes (no trailer or status, but notes have changed)
    bool onlyNotesChanged = selectedTruckDetail == null &&
        selectedFleetStatus == null &&
        hasNotesChanged();

    return onlyNotesChanged;
  }

  // Check if only notes have changed from original values
  bool hasNotesChanged() {
    String currentNotes = notes ?? '';
    String originalNote = originalNotes ?? '';
    return currentNotes != originalNote;
  }

  // Update original values (call this after successful save)
  void updateOriginalValues() {
    originalFleetId = selectedTruckDetail?.fleetId;
    originalFleetStatus = selectedFleetStatus?.id;
    originalNotes = notes;
    originalUnitNumber = selectedTruckDetail?.unitNumber;
    isEdited = false; // Reset the edit flag after successfully saving changes
  }

  void dispose() {
    notesController.dispose();
    trailerController.dispose();
  }
}

class TrailerAuditState extends ChangeNotifier {
  TruckDetail? selectedTruckDetail;
  String? area;
  String? slot;
  String? spotid;
  String? trailerStatus;
  String? notes;
  String? carrier;
  bool isLoading = false;
  GetTrailerAudit? trailerAudit;
  FleetStatus? selectedFleetStatus;
  List<FleetStatus> fleetStatuses = [
    const FleetStatus('EMPTY', 'Empty'),
    const FleetStatus('LOADED', 'Loaded')
  ];

  Spot? selectedPickupSpot;
  SpotOnLocation? selectedPickUpLocation;
  SpotOnLocationList spotOnLocationList = SpotOnLocationList(list: []);
  SpotsList pickupSpotsList = SpotsList(list: []);

  // New properties for dock management
  List<DockEntry> dockEntries = [];
  TruckListModel truckListModel = TruckListModel(list: []);

  // List to store fleet IDs that are in transit
  List<String> inTransitFleetIds = [];

  // Flag to prevent override popup during restoration
  bool _isRestoringValues = false;

  GlobalKey<AutoCompleteTextFieldState<TruckDetail>> autoCompleteKey =
      GlobalKey();
  TextEditingController autoCompleteController = TextEditingController();

  // Properties for adding new trailers
  NewTrailerReqModel newTrailerReqModel = NewTrailerReqModel(
    carrier: '',
    owner: 'A Blair',
    remarks: '',
    type: 'TRAILER',
    unitNumber: '',
    isHotTrailer: true,
    clientIds: [],
  );

  ClientListModel clientListModel = ClientListModel();
  List<String> carrierList = [];
  SpotOnClient? selectedClient;
  bool addingTruck = false;
  bool newTruckAdded = false;
  int? targetDockIndex; // Index of dock where new trailer should be added

  refresh() async {
    notifyListeners();
  }

  showLoading() {
    isLoading = true;
    notifyListeners();
  }

  hideLoading() {
    isLoading = false;
    notifyListeners();
  }

  clearAll() async {
    selectedTruckDetail = null;
    area = null;
    slot = null;
    trailerStatus = null;
    notes = null;
    carrier = null;
    trailerAudit = null;
    autoCompleteController.clear();
    selectedPickupSpot = null;
    selectedPickUpLocation = null;
    spotOnLocationList = SpotOnLocationList(list: []);
    pickupSpotsList = SpotsList(list: []);
    selectedFleetStatus = null;
    inTransitFleetIds.clear(); // Clear in-transit fleet IDs

    // Dispose controllers before clearing
    for (var entry in dockEntries) {
      entry.dispose();
    }
    dockEntries = [];
    truckListModel = TruckListModel(list: []);
    notifyListeners();
  }

  loadClients() async {
    var result = await Services.getClients();
    if (result is Success) {
      clientListModel = result.response as ClientListModel;
      notifyListeners();
    } else {
      String errorMessage = result.response as String;
      showSnackBar(errorMessage);
    }
  }

  loadCarriers() async {
    var result = await Services.getCarriers();
    if (result is Success) {
      carrierList = result.response as List<String>;
      notifyListeners();
    } else {
      String errorMessage = result.response as String;
      showSnackBar(errorMessage);
    }
  }

  setAddingTruck(bool loading) async {
    addingTruck = loading;
    notifyListeners();
  }

  setDefaultTrailerType() {
    newTrailerReqModel.type = 'TRAILER';
  }

  isValidNewTrailer() {
    if (newTrailerReqModel.unitNumber.trim().isEmpty) {
      return false;
    }
    return true;
  }

  resetNewTrailerState() {
    selectedClient = null;
    newTrailerReqModel = NewTrailerReqModel(
      carrier: '',
      owner: 'A Blair',
      remarks: '',
      type: 'TRAILER',
      unitNumber: '',
      isHotTrailer: true,
      clientIds: [],
    );
    newTruckAdded = false;
    carrierList = [];
    targetDockIndex = null;
  }

  // Add truck method for specific dock
  addTruckToSpecificDock(int dockIndex) async {
    try {
      targetDockIndex = dockIndex;
      setAddingTruck(true);

      // Get selected client
      SpotOnClient? client = await Preferences.getSelectedClient();
      if (client == null) {
        showSnackBar('No client selected', success: false);
        setAddingTruck(false);
        return false;
      }

      newTrailerReqModel.clientIds.clear();
      newTrailerReqModel.clientIds.add(client.clientId!);

      printLog(
          'Adding new trailer to dock $dockIndex: ${newTrailerReqModel.unitNumber}');

      var result = await Services.addTruck(newTrailerReqModel);

      if (result is Success) {
        String newTrailerUnitNumber = newTrailerReqModel.unitNumber;
        printLog('✅ New trailer $newTrailerUnitNumber added successfully');

        showSnackBar('Trailer added successfully');
        newTruckAdded = true;

        // Store unit number and other details before resetting
        String unitNumberToAssign = newTrailerReqModel.unitNumber;

        // Search for the newly added trailer directly instead of fetching all trucks
        TruckDetail? newlyAddedTrailer;
        try {
          List<TruckDetail> searchResults = await searchTrailers(unitNumberToAssign);
          if (searchResults.isNotEmpty) {
            newlyAddedTrailer = searchResults.firstWhere(
              (truck) => truck.unitNumber == unitNumberToAssign,
            );
            printLog(
                '✅ Found newly added trailer via search: ${newlyAddedTrailer.unitNumber}');
          } else {
            printLog('⚠️ No search results for newly added trailer: $unitNumberToAssign');
            newlyAddedTrailer = null;
          }
        } catch (e) {
          printLog(
              '⚠️ Error searching for newly added trailer: $unitNumberToAssign - $e');
          newlyAddedTrailer = null;
        }

        // Automatically assign the new trailer to the target dock only if found
        if (newlyAddedTrailer != null &&
            targetDockIndex != null &&
            targetDockIndex! >= 0 &&
            targetDockIndex! < dockEntries.length) {
          _performDockEntryUpdate(
            targetDockIndex!,
            truckDetail: newlyAddedTrailer,
            isNewTrailer: true,
          );
          printLog(
              '✅ New trailer automatically assigned to dock ${targetDockIndex! + 1}');
        } else if (newlyAddedTrailer == null) {
          printLog(
              '⚠️ Could not assign trailer to dock - trailer not found in updated list');
          showSnackBar(
              'Trailer added successfully, please select it manually from the dropdown');
        }

        // Reset the model for next use
        resetNewTrailerState();
        setAddingTruck(false);

        // Force UI refresh to show the changes
        notifyListeners();

        return true; // Success
      } else if (result is Failure) {
        newTruckAdded = false;
        String errorMessage = result.response as String;
        printLog('❌ Failed to add new trailer: $errorMessage');
        showSnackBar(errorMessage, success: false);
      }
    } catch (e) {
      printLog('ERROR in addTruckToSpecificDock: $e');
      showSnackBar('Error adding trailer: $e', success: false);
      newTruckAdded = false;
    } finally {
      setAddingTruck(false);
      // Reset the model for next use
      resetNewTrailerState();
    }

    return false; // Failure
  }

  postTrailerAudit() async {
    showLoading();
    // Change LOADED status to FULL before posting
    String? finalTrailerStatus = trailerStatus;
    if (trailerStatus == 'LOADED') {
      finalTrailerStatus = 'FULL';
    }

    var body = PostTrailerAudit(
      area: area,
      slot: slot,
      spotId: spotid,
      trailerStatus: finalTrailerStatus,
      notes: notes,
      fleetId: selectedTruckDetail?.fleetId,
      carrier: carrier,
      locationId: selectedPickUpLocation?.locationId,
    );
    printLog(body.toJson());
    var result = await Services.postTrailerAudit(postTrailerAudit: body);
    if (result is Success) {
      showSnackBar("Data saved!");
      clearAll();
      getTrailerAudit();
      getSpotOnLocations();
      refresh();
    }
    if (result is Failure) {
      printLog('Post trailer audit failed - ${result.response}');
      showSnackBar(
        "Failed to save new trailer audit!",
        success: false,
      );
    }
    clearAll();
    hideLoading();
    getTrailerAudit();
    getSpotOnLocations();
    refresh();
  }

  getTrailerAudit() async {
    showLoading();
    var result = await Services.getTrailerAudit();
    if (result is Success) {
      trailerAudit = result.response as GetTrailerAudit;
      refresh();
    }
    if (result is Failure) {
      printLog('Post trailer audit failed - ${result.response}');
      showSnackBar(
        "Failed to get trailer audit list!",
        success: false,
      );
    }
    hideLoading();
  }

  getSpots({required String locationId, bool drop = false}) async {
    showLoading();
    SpotOnClient? client = await Preferences.getSelectedClient();
    var result = await Services.getSpots(client!.clientId!, locationId);
    if (result is Success) {
      pickupSpotsList = result.response as SpotsList;
      printLog('Got ${pickupSpotsList.list.length} spots for location');

      // Remove the automatic truck list fetching - we'll only fetch when searching
      // await getTruckList();
      await getInTransitJobs(); // Call the new API to get in-transit fleet IDs

      // Create dock entries with prefilled data
      createDockEntries();
    }
    if (result is Failure) {
      printLog('Get Spots Failed');
    }
    hideLoading();
  }

  // New method to search trailers via API with debouncing
  Future<List<TruckDetail>> searchTrailers(String pattern) async {
    if (pattern.isEmpty) {
      return [];
    }

    try {
      printLog('Searching trailers for pattern: "$pattern"');
      var result = await Services.getTruckListNew(pattern);
      if (result is Success) {
        TruckListModel searchResult = result.response as TruckListModel;
        
        // Update transit status for returned trailers
        for (TruckDetail trailer in searchResult.list) {
          trailer.inTransit = trailer.fleetId != null &&
              inTransitFleetIds.contains(trailer.fleetId);
        }
        
        printLog('Found ${searchResult.list.length} trailers matching "$pattern"');
        return searchResult.list;
      } else {
        printLog('Search trailers failed');
        return [];
      }
    } catch (e) {
      printLog('Search trailers error: $e');
      return [];
    }
  }

  // Note: getTruckList method removed as we now use on-demand search via searchTrailers()

  // Method to fetch in-transit jobs and extract fleet IDs
  getInTransitJobs() async {
    try {
      printLog('Fetching in-transit jobs...');

      // Call the new API to get in-transit jobs
      var result = await Services.getAllInTransitJobs();

      if (result is Success) {
        // Parse the response structure: { "list": [{ "fleet": { "fleetId": "..." } }, ...] }
        dynamic response = result.response;
        inTransitFleetIds.clear();

        if (response is Map && response.containsKey('list')) {
          List<dynamic> jobs = response['list'] as List<dynamic>;
          for (var job in jobs) {
            if (job is Map &&
                job.containsKey('fleet') &&
                job['fleet'] != null) {
              var fleet = job['fleet'] as Map;
              if (fleet.containsKey('fleetId') && fleet['fleetId'] != null) {
                String fleetId = fleet['fleetId'].toString();
                inTransitFleetIds.add(fleetId);
              }
            }
          }
        }

        printLog(
            'Found ${inTransitFleetIds.length} in-transit fleet IDs: ${inTransitFleetIds.join(', ')}');

        // Update trailer transit status after getting fleet IDs
        _updateTrailerTransitStatus();
      } else {
        printLog('Get In-Transit Jobs Failed: ${result.response}');
        inTransitFleetIds.clear(); // Clear on failure
      }
    } catch (e) {
      printLog('Get In-Transit Jobs Error: $e');
      inTransitFleetIds.clear(); // Clear on error
    }
  }

  // Helper method to update transit status for dock entries
  void _updateTrailerTransitStatus() {
    // Since we no longer maintain a global truck list, only update dock entries
    for (DockEntry entry in dockEntries) {
      if (entry.selectedTruckDetail?.fleetId != null) {
        entry.isTransit =
            inTransitFleetIds.contains(entry.selectedTruckDetail!.fleetId);
        entry.selectedTruckDetail!.inTransit = entry.isTransit;
      }
    }
  }

  // Method to create dock entries when location is selected
  void createDockEntries() {
    if (selectedPickUpLocation == null || pickupSpotsList.list.isEmpty) {
      dockEntries = [];
      notifyListeners();
      return;
    }

    print('[TrailerAuditState] createDockEntries - Starting to create ${pickupSpotsList.list.length} dock entries');
    print('[TrailerAuditState] Available fleet statuses: ${fleetStatuses.length}');
    print('[TrailerAuditState] In-transit fleet IDs: ${inTransitFleetIds.length}');

    dockEntries = pickupSpotsList.list.map((spot) {
      DockEntry entry = DockEntry(
        spot: spot,
        isEdited: false, // Initialize isEdited to false for all new entries
        isTransit: false, // Initialize isTransit to false for all new entries
      );

      print('[TrailerAuditState] Processing dock: ${spot.spotName}');
      print('[TrailerAuditState] - Has fleet data: ${spot.fleet != null}');
      if (spot.fleet != null) {
        print('[TrailerAuditState] - Fleet status: "${spot.fleet!.fleetStatus}"');
        print('[TrailerAuditState] - Unit number: "${spot.fleet!.unitNumber}"');
        print('[TrailerAuditState] - Fleet remarks: "${spot.fleet!.remarks}"');
      }
      print('[TrailerAuditState] - Spot notes: "${spot.notes}"');

      // Prefill with data from the location response (spot.fleet)
      if (spot.fleet != null) {
        printLog(
            'Prefilling dock ${spot.spotName} with fleet data from location response');
        printLog(
            'Fleet Status: ${spot.fleet!.fleetStatus}, Unit Number: ${spot.fleet!.unitNumber}, Fleet Remarks: ${spot.fleet!.remarks}');
        printLog('Spot Remarks (Dock Notes): ${spot.remarks}');

        // 1. Prefill Status from fleet.fleetStatus
        if (spot.fleet!.fleetStatus != null) {
          print('[TrailerAuditState] - Attempting to prefill status: "${spot.fleet!.fleetStatus}"');
          try {
            // Convert FULL status to LOADED for display purposes
            String displayStatus = spot.fleet!.fleetStatus!.toUpperCase();
            if (displayStatus == 'FULL') {
              displayStatus = 'LOADED';
            }
            print('[TrailerAuditState] - Display status after conversion: "$displayStatus"');

            entry.selectedFleetStatus = fleetStatuses.firstWhere(
              (status) => status.id.toUpperCase() == displayStatus,
            );
            print('[TrailerAuditState] - ✅ Status prefilled: ${entry.selectedFleetStatus?.name}');
            printLog(
                'Found matching status: ${entry.selectedFleetStatus?.name} (original: ${spot.fleet!.fleetStatus})');
          } catch (e) {
            print('[TrailerAuditState] - ❌ Status prefill failed: $e');
            printLog(
                'No matching status found for: ${spot.fleet!.fleetStatus}');
            entry.selectedFleetStatus = null;
          }
        } else {
          print('[TrailerAuditState] - No fleet status to prefill');
        }

        // 2. Prefill Trailer name from fleet.unitNumber in the text controller
        if (spot.fleet!.unitNumber != null && spot.fleet!.unitNumber!.isNotEmpty) {
          // Set the trailer controller text directly from the fleet's unitNumber
          entry.trailerController.text = spot.fleet!.unitNumber!;
          print('[TrailerAuditState] - ✅ Trailer controller prefilled with: "${spot.fleet!.unitNumber}"');
          
          // Create a minimal TruckDetail for the existing fleet data to support dropdown behavior
          // Note: We don't fetch the full truck list here, just create a placeholder
          entry.selectedTruckDetail = TruckDetail(
            fleetId: spot.fleet!.fleetId,
            unitNumber: spot.fleet!.unitNumber,
            fleetStatus: spot.fleet!.fleetStatus,
          );
          
          // Check if this trailer is in transit
          if (spot.fleet!.fleetId != null && 
              inTransitFleetIds.contains(spot.fleet!.fleetId)) {
            entry.isTransit = true;
            entry.selectedTruckDetail!.inTransit = true;
            printLog('Dock ${spot.spotName} has trailer in transit: ${spot.fleet!.unitNumber}');
          }
          
          printLog('Prefilled dock ${spot.spotName} with trailer: ${spot.fleet!.unitNumber}');
        } else {
          print('[TrailerAuditState] - No fleet unit number to prefill');
        }

        // 3. Prefill Notes - use notes field from API response
        String? notesToUse = spot.notes; // Use notes field from API response
        if (notesToUse == null || notesToUse.isEmpty) {
          notesToUse = spot.fleet!.remarks; // Fallback to fleet remarks if no spot notes
        }
        
        entry.notes = notesToUse;
        entry.updateNotes(notesToUse ?? '');
        
        print('[TrailerAuditState] - Notes prefilled: "${notesToUse ?? ""}"');
        printLog('Notes for dock ${spot.spotName}: "${notesToUse ?? ""}" (Source: ${spot.notes != null && spot.notes!.isNotEmpty ? "spot notes" : "fleet remarks"})');
      } else if (spot.notes != null) {
        // When no fleet data, use spot notes from API response
        entry.notes = spot.notes;
        entry.updateNotes(spot.notes ?? '');
        printLog('Using spot notes from API for dock ${spot.spotName}: ${spot.notes}');
      } else {
        printLog('No fleet data or spot notes found for dock: ${spot.spotName}');
      }

      // Also check for existing trailer audit data as secondary source
      if (trailerAudit?.list != null) {
        try {
          ListElement? existingAudit = trailerAudit!.list!.firstWhere(
            (audit) =>
                audit.area == selectedPickUpLocation!.locationName &&
                audit.slot == spot.spotName,
          );

          // Only use audit data if fleet data is not available
          if (spot.fleet == null) {
            printLog(
                'Using trailer audit data as fallback for dock ${spot.spotName}');

            if (existingAudit.trailerNumber != null &&
                entry.selectedTruckDetail == null) {
              // For audit fallback, just set the controller text without full truck object
              entry.trailerController.text = existingAudit.trailerNumber!;
              entry.selectedTruckDetail = TruckDetail(
                unitNumber: existingAudit.trailerNumber,
              );
            }

            if (existingAudit.trailerStatus != null &&
                entry.selectedFleetStatus == null) {
              try {
                // Convert FULL status to LOADED for display purposes
                String displayStatus =
                    existingAudit.trailerStatus!.toUpperCase();
                if (displayStatus == 'FULL') {
                  displayStatus = 'LOADED';
                }

                entry.selectedFleetStatus = fleetStatuses.firstWhere(
                  (status) => status.id.toUpperCase() == displayStatus,
                );
              } catch (e) {
                // Ignore if not found
              }
            }

            entry.notes ??= existingAudit.notes;
            if (existingAudit.notes != null) {
              entry.updateNotes(existingAudit.notes!);
            }
          }
        } catch (e) {
          // No existing audit data found
        }
      }

      print('[TrailerAuditState] Final entry state for ${spot.spotName}:');
      print('[TrailerAuditState] - Selected truck: ${entry.selectedTruckDetail?.unitNumber}');
      print('[TrailerAuditState] - Controller text: "${entry.trailerController.text}"');
      print('[TrailerAuditState] - Selected status: ${entry.selectedFleetStatus?.name}');
      print('[TrailerAuditState] - Notes: "${entry.notes}"');
      print('[TrailerAuditState] - Is edited: ${entry.isEdited}');

      return entry;
    }).toList();

    // Summary
    int prefilledEntries = dockEntries.where((entry) => 
      entry.selectedTruckDetail != null || 
      entry.selectedFleetStatus != null || 
      (entry.notes?.isNotEmpty == true)
    ).length;
    
    print('[TrailerAuditState] =============================================');
    print('[TrailerAuditState] DOCK ENTRIES SUMMARY:');
    print('[TrailerAuditState] Total entries: ${dockEntries.length}');
    print('[TrailerAuditState] Entries with prefilled data: $prefilledEntries');
    print('[TrailerAuditState] =============================================');

    printLog(
        'Created ${dockEntries.length} dock entries for location: ${selectedPickUpLocation!.locationName}');
    notifyListeners();
  }

  // Method to clear a specific dock entry via API
  clearDockEntry(int index, {VoidCallback? onSuccess}) async {
    if (index >= 0 && index < dockEntries.length) {
      DockEntry entry = dockEntries[index];

      showLoading();

      // Call clear API with spotId
      var result = await Services.clearTrailerAudit(spotId: entry.spot.spotId!);

      if (result is Success) {
        // Clear the local entry
        entry.clear();
        showSnackBar("Dock ${entry.spot.spotName} cleared successfully!");

        // Just notify listeners to update UI without refreshing the entire list
        // This prevents the page from refreshing and losing position
        notifyListeners();

        // Call the success callback after API success
        if (onSuccess != null) {
          onSuccess();
        }
      } else {
        showSnackBar("Failed to clear dock ${entry.spot.spotName}!",
            success: false);
        printLog('Clear dock failed: ${result.response}');
      }

      hideLoading();
    }
  }

  // Method to update dock entry with transit validation
  void updateDockEntry(
    int index, {
    TruckDetail? truckDetail,
    FleetStatus? fleetStatus,
    String? notes,
  }) {
    if (index >= 0 && index < dockEntries.length) {
      DockEntry entry = dockEntries[index];
      TruckDetail? previousTruck = entry.selectedTruckDetail;
      FleetStatus? previousStatus = entry.selectedFleetStatus;
      String? previousNotes = entry.notes;

      // If this is a restoration operation, skip validation
      if (_isRestoringValues) {
        print("55t121ruckDetail: $truckDetail");

        _performDockEntryUpdate(index,
            truckDetail: truckDetail, fleetStatus: fleetStatus, notes: notes);
        return;
      }

      // Check if trailer selection is changing and either the new or current trailer is in transit
      if (truckDetail != null && truckDetail != previousTruck) {
        bool newTrailerInTransit = truckDetail.fleetId != null &&
            inTransitFleetIds.contains(truckDetail.fleetId);
        bool currentTrailerInTransit = previousTruck?.fleetId != null &&
            inTransitFleetIds.contains(previousTruck!.fleetId);

        if (newTrailerInTransit || currentTrailerInTransit) {
          // Show transit confirmation popup
          _showTransitConfirmationPopup(
            truckDetail: truckDetail,
            dockIndex: index,
            onCancel: () {
              // Restore previous values and force UI update
              printLog(
                  'Transit trailer selection cancelled for dock ${entry.spot.spotName}');
              _restoreEntryValues(
                  index, previousTruck, previousStatus, previousNotes);
            },
            onOverride: () {
              // Proceed with the selection
              _performDockEntryUpdate(index,
                  truckDetail: truckDetail,
                  fleetStatus: fleetStatus,
                  notes: notes);
              printLog(
                  'Transit trailer selection overridden for dock ${entry.spot.spotName}');
            },
          );
          return; // Don't proceed with update until user confirms
        }
      }
      print("t11ruckDetail: $truckDetail");
      // Normal update (no transit trailer involved)
      _performDockEntryUpdate(index,
          truckDetail: truckDetail, fleetStatus: fleetStatus, notes: notes);
    }
  }

  // Helper method to perform the actual dock entry update
  void _performDockEntryUpdate(
    int index, {
    TruckDetail? truckDetail,
    FleetStatus? fleetStatus,
    String? notes,
    bool isNewTrailer = false,
  }) {
    if (index >= 0 && index < dockEntries.length) {
      DockEntry entry = dockEntries[index];

      if (truckDetail != null) {
        entry.selectedTruckDetail = truckDetail;
        entry.trailerController.text = truckDetail.unitNumber ?? '';
        entry.isTransit = truckDetail.fleetId != null &&
            inTransitFleetIds.contains(truckDetail.fleetId);

        // If this is a new trailer being added, automatically set status to "Empty"
        if (isNewTrailer) {
          try {
            entry.selectedFleetStatus = fleetStatuses.firstWhere(
              (status) => status.id.toUpperCase() == 'EMPTY',
            );
            printLog(
                'New trailer added to dock ${entry.spot.spotName}: Status automatically set to Empty');
          } catch (e) {
            entry.selectedFleetStatus = null;
            printLog('Could not find Empty status, set to null instead');
          }
        } else {
          // When changing to existing trailer, try to set status based on trailer's fleet status
          if (fleetStatus == null) {
            FleetStatus? trailerFleetStatus =
                _getTrailerFleetStatus(truckDetail);
            if (trailerFleetStatus != null) {
              entry.selectedFleetStatus = trailerFleetStatus;
              printLog(
                  'Trailer ${truckDetail.unitNumber} changed in dock ${entry.spot.spotName}: Status set to ${trailerFleetStatus.name} based on trailer fleet status');
            } else {
              // Keep existing status when just changing trailer if no fleet status found
              printLog(
                  'Trailer changed in dock ${entry.spot.spotName}: No fleet status found, keeping existing status ${entry.selectedFleetStatus?.name}');
            }
          }
        }
      }

      if (fleetStatus != null) {
        entry.selectedFleetStatus = fleetStatus;
      }

      if (notes != null) {
        entry.notes = notes;
        entry.updateNotes(notes);
      }

      // Mark as edited if values have changed from original
      entry.isEdited = entry.hasChanges();

      printLog(
          'Updated dock ${entry.spot.spotName}: Trailer=${entry.selectedTruckDetail?.fleetStatus}, Status=${entry.selectedFleetStatus?.name}, Notes="${entry.notes}", IsEdited=${entry.isEdited}, IsTransit=${entry.isTransit}');

      notifyListeners();
    }
  }

  // Helper method to get fleet status for a trailer based on various sources
  FleetStatus? _getTrailerFleetStatus(TruckDetail truckDetail) {
    try {
      // 1. First check if trailer has direct fleetStatus field from API response
      if (truckDetail.fleetStatus != null &&
          truckDetail.fleetStatus!.isNotEmpty) {
        // Convert FULL status to LOADED for display purposes
        String displayStatus = truckDetail.fleetStatus!.toUpperCase();
        if (displayStatus == 'FULL') {
          displayStatus = 'LOADED';
        }

        FleetStatus? status = fleetStatuses.firstWhere(
          (status) => status.id.toUpperCase() == displayStatus,
          orElse: () => const FleetStatus('', ''),
        );
        if (status.id.isNotEmpty) {
          printLog(
              'Found fleet status from trailer direct field: ${status.name} (original: ${truckDetail.fleetStatus})');
          return status;
        }
      }

      // 2. Check if trailer has spot data with fleet status
      if (truckDetail.spot?.fleet?.fleetStatus != null) {
        // Convert FULL status to LOADED for display purposes
        String displayStatus =
            truckDetail.spot!.fleet!.fleetStatus!.toUpperCase();
        if (displayStatus == 'FULL') {
          displayStatus = 'LOADED';
        }

        FleetStatus? status = fleetStatuses.firstWhere(
          (status) => status.id.toUpperCase() == displayStatus,
          orElse: () => const FleetStatus('', ''),
        );
        if (status.id.isNotEmpty) {
          printLog(
              'Found fleet status from trailer spot data: ${status.name} (original: ${truckDetail.spot!.fleet!.fleetStatus})');
          return status;
        }
      }

      // 3. Check existing trailer audit data
      if (trailerAudit?.list != null) {
        try {
          ListElement? existingAudit = trailerAudit!.list!.firstWhere(
            (audit) => audit.trailerNumber == truckDetail.unitNumber,
          );
          if (existingAudit.trailerStatus != null) {
            // Convert FULL status to LOADED for display purposes
            String displayStatus = existingAudit.trailerStatus!.toUpperCase();
            if (displayStatus == 'FULL') {
              displayStatus = 'LOADED';
            }

            FleetStatus? status = fleetStatuses.firstWhere(
              (status) => status.id.toUpperCase() == displayStatus,
              orElse: () => const FleetStatus('', ''),
            );
            if (status.id.isNotEmpty) {
              printLog(
                  'Found fleet status from trailer audit data: ${status.name} (original: ${existingAudit.trailerStatus})');
              return status;
            }
          }
        } catch (e) {
          // Ignore if not found in audit data
        }
      }

      // 4. Default to Empty for trailers without status
      FleetStatus defaultStatus = fleetStatuses.firstWhere(
        (status) => status.id.toUpperCase() == 'EMPTY',
        orElse: () => const FleetStatus('', ''),
      );
      if (defaultStatus.id.isNotEmpty) {
        printLog(
            'Using default Empty status for trailer ${truckDetail.unitNumber}');
        return defaultStatus;
      }
    } catch (e) {
      printLog(
          'Error getting fleet status for trailer ${truckDetail.unitNumber}: $e');
    }

    return null;
  }

  // Method to show transit confirmation popup
  void _showTransitConfirmationPopup({
    required TruckDetail truckDetail,
    required int dockIndex,
    required VoidCallback onCancel,
    required VoidCallback onOverride,
  }) {
    // This will be called from the UI layer
    // For now, we'll store the callbacks and let the UI handle the popup
    _pendingTransitValidation = {
      'truckDetail': truckDetail,
      'dockIndex': dockIndex,
      'onCancel': onCancel,
      'onOverride': onOverride,
    };
    notifyListeners();
  }

  // Property to hold pending transit validation data
  Map<String, dynamic>? _pendingTransitValidation;

  // Getter for pending transit validation
  Map<String, dynamic>? get pendingTransitValidation =>
      _pendingTransitValidation;

  // Method to clear pending transit validation
  void clearPendingTransitValidation({bool notify = true}) {
    _pendingTransitValidation = null;
    if (notify) {
      notifyListeners();
    }
  }

  // Method to restore entry values to previous state
  void _restoreEntryValues(int index, TruckDetail? previousTruck,
      FleetStatus? previousStatus, String? previousNotes) {
    // Use a slight delay to ensure the popup is fully dismissed before restoring
    Future.delayed(const Duration(milliseconds: 50), () {
      if (index >= 0 && index < dockEntries.length) {
        DockEntry entry = dockEntries[index];

        // Set the restoration flag to bypass validation
        _isRestoringValues = true;

        // Restore the entry to its previous state
        entry.selectedTruckDetail = previousTruck;
        entry.selectedFleetStatus = previousStatus;
        entry.notes = previousNotes;
        entry.notesController.text = previousNotes ?? '';
        entry.trailerController.text = previousTruck?.unitNumber ?? '';

        // Update transit status for the restored truck
        if (previousTruck?.fleetId != null) {
          entry.isTransit = inTransitFleetIds.contains(previousTruck!.fleetId);
        } else {
          entry.isTransit = false;
        }

        // Reset isEdited flag based on restored values
        entry.isEdited = entry.hasChanges();

        // Increment rebuild counter to force UI rebuild
        entry.rebuildCounter++;

        printLog(
            'Restored dock ${entry.spot.spotName} to previous values: Trailer=${previousTruck?.unitNumber}, Status=${previousStatus?.name}, Notes="$previousNotes"');

        // Clear pending validation first without notifying
        clearPendingTransitValidation(notify: false);

        // Reset the restoration flag
        _isRestoringValues = false;

        // Multiple rebuild attempts to ensure UI synchronization
        notifyListeners();

        // Schedule additional rebuilds
        Future.delayed(
            const Duration(milliseconds: 10), () => notifyListeners());
        Future.delayed(
            const Duration(milliseconds: 100), () => notifyListeners());
      }
    });
  }

  // Method to save all dock entries using bulk API
  saveDockEntries({VoidCallback? onSuccess}) async {
    // Get only the edited dock entries that are valid for saving
    List<DockEntry> editedEntries = dockEntries
        .where((entry) => entry.isEdited && entry.isValidForSaving())
        .toList();

    if (editedEntries.isEmpty) {
      showSnackBar("No changes to save!");
      return;
    }

    showLoading();

    try {
      // Prepare bulk audit data
      List<BulkTrailerAuditItem> auditItems = editedEntries.map((entry) {
        // Change LOADED status to FULL before posting
        String? finalTrailerStatus = entry.selectedFleetStatus?.id;
        if (finalTrailerStatus == 'LOADED') {
          finalTrailerStatus = 'FULL';
        }

        return BulkTrailerAuditItem(
          fleetId: entry.selectedTruckDetail?.fleetId,
          trailerStatus: finalTrailerStatus,
          notes: entry.notes,
          spotId: entry.spot.spotId,
        );
      }).toList();

      printLog('Saving ${auditItems.length} edited dock entries...');

      // Call bulk save API
      var result =
          await Services.postBulkTrailerAudit(trailerAuditList: auditItems);

      if (result is Success) {
        // Update original values for saved entries to reset isEdited flag
        for (var entry in editedEntries) {
          entry.updateOriginalValues();
        }

        showSnackBar("Data saved!");
        notifyListeners(); // Refresh UI to remove edit indicators

        // Call the success callback after API success
        if (onSuccess != null) {
          onSuccess();
        }
      } else {
        showSnackBar("Failed to save dock entries!", success: false);
        printLog('Bulk save failed: ${result.response}');
      }
    } catch (e) {
      showSnackBar("Error saving dock entries!", success: false);
      printLog('Bulk save error: $e');
    }

    hideLoading();
  }

  getSpotOnLocations() async {
    showLoading();
    SpotOnClient? client = await Preferences.getSelectedClient();
    var result = await Services.getLocationSpots(client!.clientId!, '');
    if (result is Success) {
      spotOnLocationList = result.response as SpotOnLocationList;
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
    }
    hideLoading();
  }

  init() {}
}
