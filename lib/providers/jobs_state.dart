import 'package:spot_on/models/job/jobs_list_model.dart';
import 'package:spot_on/utils/imports.dart';

class JobsState extends ChangeNotifier {
  bool jobsLoading = false;
  bool homeJobsLoading = false;
  bool confirmPickUpLoading = false;
  bool jobUpdating = false;
  JobsListModel jobsListModel = JobsListModel(list: []);
  JobsListModel homeJobsListModel = JobsListModel(list: []);
  int jobsCount = 0;
  String notes = '';
  String sequence = '';

  setJobUpdating(bool loading) async {
    jobUpdating = loading;
    notifyListeners();
  }

  setConfirmPickUpLoading(bool loading) async {
    confirmPickUpLoading = loading;
    notifyListeners();
  }

  setHomeJobsLoading(bool loading) async {
    homeJobsLoading = loading;
    notifyListeners();
  }

  setJobsLoading(bool loading) async {
    jobsLoading = loading;
    notifyListeners();
  }

  getJobs() async {
    setJobsLoading(true);
    String status = 'status=COMPLETED';
    if (isGuard()) {
      status = '';
    }
    var result = await Services.getJobs(filter: status);
    if (result is Success) {
      jobsListModel = result.response as JobsListModel;
      jobsCount = 0;
      for (int i = 0; i < jobsListModel.list!.length; i++) {
        Job job = jobsListModel.list![i];
        if (null != job.dropDateTime && isTodayJob(job.dropDateTime!)) {
          jobsCount++;
        }
      }
      // print("haha $jobsCount");
      notifyListeners();
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
      showSnackBar(
        result.response.toString(),
        delay: 10,
      );
    }
    setJobsLoading(false);
  }

  getHomeJobs() async {
    setHomeJobsLoading(true);
    var result = await Services.getHomeJobs();
    if (result is Success) {
      JobsListModel listModel = result.response as JobsListModel;
      homeJobsListModel.list!.clear();
      homeJobsListModel.list!.addAll(listModel.list!);
    }

    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
      showSnackBar(
        result.response.toString(),
        delay: 10,
      );
    }
    setHomeJobsLoading(false);
  }

  doJobUpdate(Job job, String notes, DateTime dateTime,
      {String? lat, String? lng}) async {
    setJobUpdating(true);
    var result = await Services.doJobUpdate(
      jobId: job.jobId!,
      jobUpdateStatus: job.jobUpdateStatus!,
      lat: lat,
      lng: lng,
      notes: notes,
    );
    if (result is Success) {
      showSnackBar('Spot Updated Successfully');
      getJobsForHomeTab();
      getJobs();
    }
    if (result is Failure) {
      // print("${result.response} 2");
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
      showSnackBar(
        result.response.toString(),
        delay: 10,
      );
    }
    setJobUpdating(false);
  }
}
