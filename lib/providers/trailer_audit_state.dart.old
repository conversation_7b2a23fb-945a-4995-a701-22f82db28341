import 'package:autocomplete_textfield/autocomplete_textfield.dart';
import 'package:spot_on/models/traileraudit/get_trailer_audit.dart';
import 'package:spot_on/models/traileraudit/post_trailer_audit.dart';
import 'package:spot_on/models/traileraudit/bulk_trailer_audit.dart';
import 'package:spot_on/models/truck_list_model.dart';
import 'package:spot_on/models/new_trailer_req_model.dart';
import 'package:spot_on/utils/imports.dart';

import '../models/cliient_list_model.dart';
import '../models/spot_on_locations.dart';
import '../models/spots_list.dart';
import '../utils/app_logger.dart';
import 'create_job_state.dart';

// Class to represent individual dock entries
class DockEntry {
  final Spot spot;
  TruckDetail? selectedTruckDetail;
  FleetStatus? selectedFleetStatus;
  String? notes;
  ListElement? existingAudit; // Reference to existing trailer audit data
  late TextEditingController notesController;
  bool isEdited = false; // Flag to track if any field has been edited
  bool isTransit = false; // Flag to track if trailer is in transit

  // Original values from spot data when location is selected (baseline)
  String? originalFleetId;        // From spot.fleet.fleetId
  String? originalFleetStatus;    // From spot.fleet.fleetStatus
  String? originalNotes;          // From spot.fleet.remarks or spot.remarks
  String? originalUnitNumber;     // From spot.fleet.unitNumber

  DockEntry({
    required this.spot,
    this.selectedTruckDetail,
    this.selectedFleetStatus,
    this.notes,
    this.existingAudit,
    this.isEdited = false, // Initialize to false by default
    this.isTransit = false, // Initialize to false by default
  }) {
    notesController = TextEditingController(text: notes ?? '');
    // Store original values from spot data as baseline
    _storeOriginalSpotData();
  }

  // Store original values from spot data
  void _storeOriginalSpotData() {
    if (spot.fleet != null) {
      originalFleetId = spot.fleet!.fleetId;
      originalFleetStatus = spot.fleet!.fleetStatus;
      originalUnitNumber = spot.fleet!.unitNumber;
      
      // Use notes field from API response, fallback to fleet remarks
      originalNotes = spot.notes; // Use notes field from API response
      if (originalNotes == null || originalNotes!.isEmpty) {
        originalNotes = spot.fleet!.remarks; // Fallback to fleet remarks
      }
    } else {
      originalFleetId = null;
      originalFleetStatus = null;
      originalUnitNumber = null;
      originalNotes = spot.notes; // Use notes field from API response when no fleet data
    }

    printLog('Stored original data for ${spot.spotName}: FleetId=${originalFleetId}, Status=${originalFleetStatus}, UnitNumber=${originalUnitNumber}, Notes="${originalNotes}" (Source: ${spot.notes != null && spot.notes!.isNotEmpty ? "spot notes" : "fleet remarks"})');
  }

  void clear() {
    selectedTruckDetail = null;
    selectedFleetStatus = null;
    notes = null;
    notesController.clear();
    isEdited = false; // Reset the edit flag when cleared
  }

  void updateNotes(String newNotes) {
    notes = newNotes;
    if (notesController.text != newNotes) {
      notesController.text = newNotes;
    }
  }

  // Check if current form values are different from original spot data
  // Focus on: dock (spotName), status (fleetStatus), unitNumber
  bool hasChanges() {
    // Get current form values
    String currentStatus = selectedFleetStatus?.id ?? '';
    String currentUnitNumber = selectedTruckDetail?.unitNumber ?? '';
    String currentNotes = notes ?? '';

    // Get original values from spot data
    String originalStatus = originalFleetStatus ?? '';
    String originalUnit = originalUnitNumber ?? '';
    String originalNote = originalNotes ?? '';

    // Compare the key fields: status, unitNumber, notes
    bool statusChanged = currentStatus != originalStatus;
    bool unitNumberChanged = currentUnitNumber != originalUnit;
    bool notesChanged = currentNotes != originalNote;

    bool hasChanges = statusChanged || unitNumberChanged || notesChanged;

    if (hasChanges) {
      printLog('Changes detected for dock ${spot.spotName}:');
      printLog('  Status: "$originalStatus" → "$currentStatus" ${statusChanged ? "(CHANGED)" : ""}');
      printLog('  UnitNumber: "$originalUnit" → "$currentUnitNumber" ${unitNumberChanged ? "(CHANGED)" : ""}');
      printLog('  Notes: "$originalNote" → "$currentNotes" ${notesChanged ? "(CHANGED)" : ""}');
    }

    return hasChanges;
  }

  // Check if all required fields are filled for this entry
  bool hasAllRequiredFields() {
    return selectedTruckDetail != null && selectedFleetStatus != null;
  }

  // Update original values (call this after successful save)
  void updateOriginalValues() {
    originalFleetId = selectedTruckDetail?.fleetId;
    originalFleetStatus = selectedFleetStatus?.id;
    originalNotes = notes;
    originalUnitNumber = selectedTruckDetail?.unitNumber;
    isEdited = false; // Reset the edit flag after successfully saving changes
  }

  void dispose() {
    notesController.dispose();
  }
}

class TrailerAuditState extends ChangeNotifier {
  TruckDetail? selectedTruckDetail;
  String? area;
  String? slot;
  String? spotid;
  String? trailerStatus;
  String? notes;
  String? carrier;
  bool isLoading = false;
  GetTrailerAudit? trailerAudit;
  FleetStatus? selectedFleetStatus;
  List<FleetStatus> fleetStatuses = [const FleetStatus('EMPTY', 'Empty'),const FleetStatus('FULL', 'Full')];

  Spot? selectedPickupSpot;
  SpotOnLocation? selectedPickUpLocation;
  SpotOnLocationList spotOnLocationList = SpotOnLocationList(list: []);
  SpotsList pickupSpotsList = SpotsList(list: []);

  // New properties for dock management
  List<DockEntry> dockEntries = [];
  TruckListModel truckListModel = TruckListModel(list: []);
  
  // List to store fleet IDs that are in transit
  List<String> inTransitFleetIds = [];
  
  // Flag to prevent override popup during restoration
  bool _isRestoringValues = false;

  GlobalKey<AutoCompleteTextFieldState<TruckDetail>> autoCompleteKey = GlobalKey();
  TextEditingController autoCompleteController = TextEditingController();

  // Properties for adding new trailers
  NewTrailerReqModel newTrailerReqModel = NewTrailerReqModel(
    carrier: '',
    owner: 'A Blair',
    remarks: '',
    type: 'TRAILER',
    unitNumber: '',
    isHotTrailer: true,
    clientIds: [],
  );

  ClientListModel clientListModel = ClientListModel();
  List<String> carrierList = [];
  SpotOnClient? selectedClient;
  bool addingTruck = false;
  bool newTruckAdded = false;
  int? targetDockIndex; // Index of dock where new trailer should be added

  refresh() async {
    notifyListeners();
  }

  showLoading() {
    isLoading = true;
    notifyListeners();
  }

  hideLoading() {
    isLoading = false;
    notifyListeners();
  }

  clearAll() async {
    selectedTruckDetail = null;
    area = null;
    slot = null;
    trailerStatus = null;
    notes = null;
    carrier = null;
    trailerAudit = null;
    autoCompleteController.clear();
    selectedPickupSpot = null;
    selectedPickUpLocation = null;
    spotOnLocationList = SpotOnLocationList(list: []);
    pickupSpotsList = SpotsList(list: []);
    selectedFleetStatus = null;
    inTransitFleetIds.clear(); // Clear in-transit fleet IDs

    // Dispose controllers before clearing
    for (var entry in dockEntries) {
      entry.dispose();
    }
    dockEntries = [];
    truckListModel = TruckListModel(list: []);
    notifyListeners();
  }

  // Method to create dock entries when location is selected
  void createDockEntries() {
    if (selectedPickUpLocation == null || pickupSpotsList.list.isEmpty) {
      dockEntries = [];
      notifyListeners();
      return;
    }

    dockEntries = pickupSpotsList.list.map((spot) {
      DockEntry entry = DockEntry(
        spot: spot,
        isEdited: false, // Initialize isEdited to false for all new entries
        isTransit: false, // Initialize isTransit to false for all new entries
      );

      // Prefill with data from the location response (spot.fleet)
      if (spot.fleet != null) {
        printLog('Prefilling dock ${spot.spotName} with fleet data from location response');
        printLog('Fleet Status: ${spot.fleet!.fleetStatus}, Unit Number: ${spot.fleet!.unitNumber}, Fleet Remarks: ${spot.fleet!.remarks}');
        printLog('Spot Remarks (Dock Notes): ${spot.remarks}');

        // 1. Prefill Status from fleet.fleetStatus
        if (spot.fleet!.fleetStatus != null) {
          try {
            entry.selectedFleetStatus = fleetStatuses.firstWhere(
              (status) => status.id.toUpperCase() == spot.fleet!.fleetStatus!.toUpperCase(),
            );
            printLog('Found matching status: ${entry.selectedFleetStatus?.name}');
          } catch (e) {
            printLog('No matching status found for: ${spot.fleet!.fleetStatus}');
            entry.selectedFleetStatus = null;
          }
        }

        // 2. Prefill Trailer from fleet.unitNumber
        if (spot.fleet!.unitNumber != null && truckListModel.list.isNotEmpty) {
          try {
            entry.selectedTruckDetail = truckListModel.list.firstWhere(
              (truck) => truck.unitNumber == spot.fleet!.unitNumber,
            );
            printLog('Found matching truck: ${entry.selectedTruckDetail?.unitNumber}');
            
            // Check if this trailer is in transit
            if (entry.selectedTruckDetail?.fleetId != null && 
                inTransitFleetIds.contains(entry.selectedTruckDetail!.fleetId)) {
              entry.isTransit = true;
              printLog('Dock ${spot.spotName} has trailer in transit: ${entry.selectedTruckDetail?.unitNumber}');
            }
          } catch (e) {
            printLog('No matching truck found for unit number: ${spot.fleet!.unitNumber}');
            entry.selectedTruckDetail = null;
          }
        }

        // 3. Prefill Notes - use notes field from API response
        String? notesToUse = spot.notes; // Use notes field from API response
        if (notesToUse == null || notesToUse.isEmpty) {
          notesToUse = spot.fleet!.remarks; // Fallback to fleet remarks if no spot notes
        }
        
        entry.notes = notesToUse;
        entry.notesController.text = notesToUse ?? '';
        
        printLog('Notes for dock ${spot.spotName}: "${notesToUse ?? ""}" (Source: ${spot.notes != null && spot.notes!.isNotEmpty ? "spot notes" : "fleet remarks"})');
      } else if (spot.notes != null) {
        // When no fleet data, use spot notes from API response
        entry.notes = spot.notes;
        entry.notesController.text = spot.notes ?? '';
        printLog('Using spot notes from API for dock ${spot.spotName}: ${spot.notes}');
      } else {
        printLog('No fleet data or spot notes found for dock: ${spot.spotName}');
      }

      // Also check for existing trailer audit data as secondary source
      if (trailerAudit?.list != null) {
        try {
          ListElement? existingAudit = trailerAudit!.list!.firstWhere(
            (audit) => audit.area == selectedPickUpLocation!.locationName &&
                      audit.slot == spot.spotName,
          );

          // Only use audit data if fleet data is not available
          if (spot.fleet == null) {
            printLog('Using trailer audit data as fallback for dock ${spot.spotName}');

            if (existingAudit.trailerNumber != null && truckListModel.list.isNotEmpty && entry.selectedTruckDetail == null) {
              try {
                entry.selectedTruckDetail = truckListModel.list.firstWhere(
                  (truck) => truck.unitNumber == existingAudit.trailerNumber,
                );
              } catch (e) {
                // Ignore if not found
              }
            }

            if (existingAudit.trailerStatus != null && entry.selectedFleetStatus == null) {
              try {
                entry.selectedFleetStatus = fleetStatuses.firstWhere(
                  (status) => status.id == existingAudit.trailerStatus,
                );
              } catch (e) {
                // Ignore if not found
              }
            }

            entry.notes ??= existingAudit.notes;
          }
        } catch (e) {
          // No existing audit data found
        }
      }

      return entry;
    }).toList();

    printLog('Created ${dockEntries.length} dock entries for location: ${selectedPickUpLocation!.locationName}');
    notifyListeners();
  }

  // Method to clear a specific dock entry via API
  clearDockEntry(int index) async {
    if (index >= 0 && index < dockEntries.length) {
      DockEntry entry = dockEntries[index];

      showLoading();

      // Call clear API with spotId
      var result = await Services.clearTrailerAudit(spotId: entry.spot.spotId!);

      if (result is Success) {
        // Clear the local entry
        entry.clear();
        showSnackBar("Dock ${entry.spot.spotName} cleared successfully!");

        // Refresh data
        // getTrailerAudit();--edited
        if (selectedPickUpLocation != null) {
          getSpots(locationId: selectedPickUpLocation!.locationId!, drop: false);
        }
      } else {
        showSnackBar("Failed to clear dock ${entry.spot.spotName}!", success: false);
        printLog('Clear dock failed: ${result.response}');
      }

      hideLoading();
    }
  }

  // Method to update dock entry
  void updateDockEntry(int index, {
    TruckDetail? truckDetail,
    FleetStatus? fleetStatus,
    String? notes,
  }) {
    
    if (index >= 0 && index < dockEntries.length) {
      bool changed = false;
      
      if (truckDetail != null) {
        // Get current truck detail for comparison
        TruckDetail? currentTruck = dockEntries[index].selectedTruckDetail;
        bool currentTruckIsInTransit = currentTruck?.fleetId != null && inTransitFleetIds.contains(currentTruck!.fleetId);
        bool newTruckIsInTransit = truckDetail.fleetId != null && inTransitFleetIds.contains(truckDetail.fleetId);
        
        // Show override popup in two cases (but skip if we're restoring values):
        // 1. Trying to select an in-transit trailer
        // 2. Trying to change FROM an in-transit trailer to something else
        if (!_isRestoringValues && (newTruckIsInTransit || currentTruckIsInTransit)) {
          // Store the current truck detail to restore on cancel
          TruckDetail? truckToRestoreOnCancel = currentTruck;
          
          // Immediately trigger a UI refresh to revert dropdown to previous state
          notifyListeners();
          
          // Determine popup message and cancel behavior
          String popupTitle;
          String popupMessage;
          bool shouldSetNullOnCancel;
          
          if (newTruckIsInTransit) {
                                    notifyListeners();

            // Case 1: Trying to select an in-transit trailer
            popupTitle = 'Trailer In Transit';
            popupMessage = 'Trailer ${truckDetail.unitNumber ?? "Unknown"} is currently in transit. Do you want to override and select this trailer?';
            shouldSetNullOnCancel = currentTruck == null; // Set to null only if no previous selection
          } else {
            // Case 2: Trying to change FROM an in-transit trailer
            popupTitle = 'Change In-Transit Trailer';
            popupMessage = 'Current trailer ${currentTruck?.unitNumber ?? "Unknown"} is in transit. Do you want to override and change to ${truckDetail.unitNumber ?? "Unknown"}?';
            shouldSetNullOnCancel = false; 
            // Keep current in-transit trailer
          }
          
          // Show override popup
          showDialog(
            context: globalKey.currentContext!,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return Dialog(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.85,
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        popupTitle,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 16),
                      
                      // Content
                      Text(
                        popupMessage,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 32),
                      
                      // Buttons
                      Row(
                        children: [
                          // Cancel Button
                          Expanded(
                            child: Container(
                              height: 48,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: TextButton(
                                                                                                onPressed: () {
                                  Navigator.of(context).pop();
                                  
                                  // Handle cancel based on the scenario - with delay to ensure dialog closes first
                                  Future.delayed(const Duration(milliseconds: 50), () {
                                    if (shouldSetNullOnCancel) {
                                      // Case 1: Set truck detail to null (no previous selection or previous was not in-transit)
                                      dockEntries[index].selectedTruckDetail = null;
                                      dockEntries[index].isEdited = true; // Mark as edited since user made a change
                                      printLog('User cancelled selection of in-transit trailer: ${truckDetail.unitNumber} - set truck detail to null');
                                    } else {
                                      // Case 2: Keep current in-transit trailer (revert to stored selection)
                                      dockEntries[index].selectedTruckDetail = truckToRestoreOnCancel;
                                      dockEntries[index].isEdited = true; // Mark as edited since user made a change

                                      printLog('User cancelled change from in-transit trailer: ${truckToRestoreOnCancel?.unitNumber} - restored current trailer');
                                    }
                                    
                                    // Force immediate UI refresh
                                    notifyListeners();
                                    
                                    // Additional refresh to ensure dropdown updates
                                    Future.delayed(const Duration(milliseconds: 10), () {
                                      dockEntries[index].isEdited = false; // Mark as edited since user made a change

                                      notifyListeners();
                                    });
                                  });
                                    notifyListeners();

                                },
                                child: const Text(
                                  'CANCEL',
                                  style: TextStyle(
                                    color: Colors.black87,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          // Override Button
                          Expanded(
                            child: SizedBox(
                              height: 48,
                              child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  // Proceed with the selection - user confirmed override
                                  printLog('User confirmed override for in-transit trailer: ${truckDetail.unitNumber}');
                                  _proceedWithTrailerSelection(index, truckDetail);
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.greenAccent,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                                child: const Text(
                                  'OVERRIDE',
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
              
            },
          );
                                    notifyListeners();

          // printLog('Warning: Trailer ${truckDetail.unitNumber} (Fleet ID: ${truckDetail.fleetId}) is in transit - showing override dialog');
          // Don't update the trailer selection - let user decide through the dialog
          return; // Early return to prevent any assignment
        }
        
        // If not in transit, proceed normally
        _proceedWithTrailerSelection(index, truckDetail);
        changed = true;
      }
      if (fleetStatus != null) {
        dockEntries[index].selectedFleetStatus = fleetStatus;
        changed = true;
      }
      if (notes != null) {
                                    notifyListeners();

        dockEntries[index].notes = notes;
        // Ensure the notes controller is also updated if it's different
        if (dockEntries[index].notesController.text != notes) {
          dockEntries[index].notesController.text = notes;
        }
        changed = true;
      }
      
      // If any field was changed, mark this dock as edited
      if (changed) {
                                    notifyListeners();

        dockEntries[index].isEdited = true;
        printLog('Dock ${dockEntries[index].spot.spotName} marked as edited');
      }
      
      notifyListeners();
    }
  }

  // Helper method to proceed with trailer selection
  void _proceedWithTrailerSelection(int index, TruckDetail truckDetail) {
    dockEntries[index].selectedTruckDetail = truckDetail;
    dockEntries[index].isEdited = true;
    printLog('Dock ${dockEntries[index].spot.spotName} marked as edited with trailer ${truckDetail.unitNumber}');
    notifyListeners();
  }

  // Helper method to restore dock to original values
  void _restoreDockToOriginalValues(int index) {
    if (index >= 0 && index < dockEntries.length) {
      DockEntry entry = dockEntries[index];
      
      // Set flag to prevent override popup during restoration
      _isRestoringValues = true;
      
      // Restore trailer selection based on original unit number
      if (entry.originalUnitNumber != null && truckListModel.list.isNotEmpty) {
        try {
          entry.selectedTruckDetail = truckListModel.list.firstWhere(
            (truck) => truck.unitNumber == entry.originalUnitNumber,
          );
          printLog('Restored trailer: ${entry.originalUnitNumber}');
        } catch (e) {
          entry.selectedTruckDetail = null;
          printLog('Could not find original trailer: ${entry.originalUnitNumber}');
        }
      } else {
        entry.selectedTruckDetail = null;
        printLog('No original trailer to restore');
      }
      
      // Restore fleet status based on original status
      if (entry.originalFleetStatus != null) {
        try {
          entry.selectedFleetStatus = fleetStatuses.firstWhere(
            (status) => status.id.toUpperCase() == entry.originalFleetStatus!.toUpperCase(),
          );
          printLog('Restored status: ${entry.originalFleetStatus}');
        } catch (e) {
          entry.selectedFleetStatus = null;
          printLog('Could not find original status: ${entry.originalFleetStatus}');
        }
      } else {
        entry.selectedFleetStatus = null;
        printLog('No original status to restore');
      }
      
      // Restore notes
      entry.notes = entry.originalNotes;
      entry.notesController.text = entry.originalNotes ?? '';
      printLog('Restored notes: "${entry.originalNotes ?? ""}"');
      
      // Reset edited flag since we're back to original values
      entry.isEdited = false;
      
      // Reset the restoration flag
      _isRestoringValues = false;
      
      printLog('Dock ${entry.spot.spotName} restored to original values');
    }
  }

  postTrailerAudit() async {
    showLoading();
    var body = PostTrailerAudit(
      area: area,
      slot: slot,
      spotId: spotid,
      trailerStatus: trailerStatus,
      notes: notes,
      fleetId: selectedTruckDetail?.fleetId,
      carrier: carrier,
      locationId: selectedPickUpLocation?.locationId,
    );
    printLog(body.toJson());
    var result = await Services.postTrailerAudit(postTrailerAudit: body);
    if (result is Success) {
      showSnackBar("New trailer audit saved successfully!");
      clearAll();
      getTrailerAudit();
      getSpotOnLocations();
      refresh();
    }
    if (result is Failure) {
      printLog('Post trailer audit failed - ${result.response}');
      showSnackBar(
        "Failed to save new trailer audit!",
        success: false,
      );
    }
    clearAll();
    hideLoading();
    getTrailerAudit();
    getSpotOnLocations();
    refresh();
  }

  getTrailerAudit() async {
    showLoading();
    var result = await Services.getTrailerAudit();
    if (result is Success) {
      trailerAudit = result.response as GetTrailerAudit;
      printLog('Got ${trailerAudit?.list?.length ?? 0} trailer audit records');

      // If we have a selected location and dock entries, refresh them with new data
      if (selectedPickUpLocation != null && dockEntries.isNotEmpty) {
        createDockEntries();
      }

      refresh();
    }
    if (result is Failure) {
      printLog('Get trailer audit failed - ${result.response}');
      showSnackBar(
        "Failed to get trailer audit list!",
        success: false,
      );
    }
    hideLoading();
  }

  getSpots({required String locationId, bool drop = false}) async {
    showLoading();
    SpotOnClient? client = await Preferences.getSelectedClient();
    var result = await Services.getSpots(client!.clientId!, locationId);
    if (result is Success) {
      pickupSpotsList = result.response as SpotsList;
      printLog('Got ${pickupSpotsList.list.length} spots for location');

      // Get truck list and in-transit jobs
      await getTruckList();
      await getInTransitJobs(); // Call the new API to get in-transit fleet IDs

      // Create dock entries with prefilled data
      createDockEntries();
    }
    if (result is Failure) {
      printLog('Get Spots Failed');
    }
    hideLoading();
  }

  // Method to get truck list for dropdowns
  getTruckList() async {
    try {
      var result = await Services.getTruckListNew('');
      if (result is Success) {
        truckListModel = result.response as TruckListModel;
      }
      if (result is Failure) {
        printLog('Get Truck List Failed');
      }
    } catch (e) {
      printLog('Get Truck List Error: $e');
    }
  }

  // Method to search for a specific trailer by unit number
  searchSpecificTrailer(String unitNumber) async {
    try {
      printLog('Searching for specific trailer: $unitNumber');
      var result = await Services.getTruckListNew(unitNumber);
      if (result is Success) {
        TruckListModel searchResult = result.response as TruckListModel;
        
        // If the specific trailer is found, add it to the current truck list if not already present
        if (searchResult.list.isNotEmpty) {
          for (var newTrailer in searchResult.list) {
            if (newTrailer.unitNumber == unitNumber) {
              // Check if this trailer is already in the main list
              bool alreadyExists = truckListModel.list.any((truck) => truck.unitNumber == unitNumber);
              
              if (!alreadyExists) {
                // Add the new trailer to the existing truck list
                List<TruckDetail> updatedList = List.from(truckListModel.list);
                updatedList.add(newTrailer);
                truckListModel = TruckListModel(
                  list: updatedList,
                  page: truckListModel.page,
                  size: truckListModel.size + 1,
                  totalElements: truckListModel.totalElements + 1,
                );
                printLog('Added new trailer $unitNumber to truck list');
              } else {
                printLog('Trailer $unitNumber already exists in truck list');
              }
              return newTrailer;
            }
          }
        }
      }
      return null;
    } catch (e) {
      printLog('Search Specific Trailer Error: $e');
      return null;
    }
  }

  // Method to get in-transit jobs and extract fleet IDs
  getInTransitJobs() async {
    try {
      printLog('Fetching in-transit jobs...');
      
      // Call the new API to get in-transit jobs
      var result = await Services.getInTransitJobs();
      
      if (result is Success) {
        // Parse the response structure: { "list": [{ "fleet": { "fleetId": "..." } }, ...] }
        dynamic response = result.response;
        inTransitFleetIds.clear();
        
        if (response is Map && response.containsKey('list')) {
          List<dynamic> jobs = response['list'] as List<dynamic>;
          for (var job in jobs) {
            if (job is Map && job.containsKey('fleet') && job['fleet'] != null) {
              var fleet = job['fleet'] as Map;
              if (fleet.containsKey('fleetId') && fleet['fleetId'] != null) {
                String fleetId = fleet['fleetId'].toString();
                inTransitFleetIds.add(fleetId);
              }
            }
          }
        }
        
        printLog('Found ${inTransitFleetIds.length} in-transit fleet IDs: ${inTransitFleetIds.join(', ')}');
      } else {
        printLog('Get In-Transit Jobs Failed: ${result.response}');
        inTransitFleetIds.clear(); // Clear on failure
      }
    } catch (e) {
      printLog('Get In-Transit Jobs Error: $e');
      inTransitFleetIds.clear(); // Clear on error
    }
  }

  getSpotOnLocations() async {
    showLoading();
    SpotOnClient? client = await Preferences.getSelectedClient();
    var result = await Services.getLocationSpots(client!.clientId!,'');
    if (result is Success) {
      spotOnLocationList = result.response as SpotOnLocationList;
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
    }
    hideLoading();
  }

  // Method to save all dock entries using bulk API
  // Only save docks that have isEdited = true
  saveDockEntries() async {
    if (selectedPickUpLocation == null || dockEntries.isEmpty) {
      showSnackBar("No dock entries to save!", success: false);
      return;
    }

    printLog('=== CHECKING EDITED DOCKS ===');
    printLog('Total docks: ${dockEntries.length}');

    // Filter docks that have been edited
    List<DockEntry> editedDocks = dockEntries.where((entry) => entry.isEdited).toList();
    List<String> validationErrors = [];

    printLog('Found ${editedDocks.length} edited docks');

    // Validate that all edited docks have required fields
    for (int i = 0; i < editedDocks.length; i++) {
      DockEntry entry = editedDocks[i];
      String dockName = entry.spot.spotName ?? 'Edited Dock ${i + 1}';

      printLog('--- Validating edited dock: $dockName ---');
      printLog('Status: ${entry.selectedFleetStatus?.name ?? "NOT SET"}');
      printLog('Trailer: ${entry.selectedTruckDetail?.unitNumber ?? "NOT SET"}');
      printLog('Notes: "${entry.notes ?? ""}"');

      // Check if edited dock has all required fields
      if (!entry.hasAllRequiredFields()) {
        List<String> missingFields = [];

        if (entry.selectedTruckDetail == null) {
          missingFields.add("Trailer");
        }

        if (entry.selectedFleetStatus == null) {
          missingFields.add("Status");
        }

        validationErrors.add("$dockName: Missing ${missingFields.join(', ')}");
        printLog('❌ VALIDATION FAILED for $dockName: Missing ${missingFields.join(', ')}');
      } else {
        printLog('✅ VALIDATION PASSED for $dockName');
      }
    }

    printLog('=== VALIDATION COMPLETE ===');
    printLog('Total edited docks: ${editedDocks.length}');
    printLog('Validation errors: ${validationErrors.length}');

    // Check if there are any edited docks
    if (editedDocks.isEmpty) {
      showSnackBar("No edited docks found to save!", success: false);
      printLog('No API call needed - no edited docks detected');
      return;
    }

    // Check for validation errors in edited docks
    if (validationErrors.isNotEmpty) {
      String errorMessage = "Please fill required fields in edited docks:\n${validationErrors.join('\n')}";
      showSnackBar(errorMessage, success: false);
      printLog('Save cancelled due to validation errors in edited docks');
      return;
    }

    showLoading();

    // Arrange edited docks into payload list
    List<BulkTrailerAuditItem> payload = [];

    for (DockEntry entry in editedDocks) {
      payload.add(BulkTrailerAuditItem(
        fleetId: entry.selectedTruckDetail?.fleetId,
        trailerStatus: entry.selectedFleetStatus?.id,
        notes: entry.notes,
        spotId: entry.spot.spotId,
      ));
    }

    printLog('=== PAYLOAD PREPARED ===');
    printLog('Sending ${payload.length} edited docks to API');
    printLog('Edited dock names: ${editedDocks.map((e) => e.spot.spotName).join(', ')}');

    // Call bulk save API with payload
    var result = await Services.postBulkTrailerAudit(trailerAuditList: payload);

    if (result is Success) {
      // Update original values and reset isEdited flag after successful save
      for (DockEntry entry in editedDocks) {
        entry.updateOriginalValues(); // This also resets isEdited to false
      }

      showSnackBar("${editedDocks.length} edited dock entries saved successfully!");
      //edited docks are not updated in the list, so we need to refresh the list
      // getTrailerAudit();
      if (selectedPickUpLocation != null) {
        getSpots(locationId: selectedPickUpLocation!.locationId!, drop: false);
      }
    } else {
      showSnackBar("Failed to save edited dock entries!", success: false);
      printLog('Bulk save failed: ${result.response}');
    }

    hideLoading();
  }

  // Methods for adding new trailers
  loadClients() async {
    var result = await Services.getClients();
    if (result is Success) {
      clientListModel = result.response as ClientListModel;
      notifyListeners();
    } else {
      String errorMessage = result.response as String;
      showSnackBar(errorMessage);
    }
  }

  loadCarriers() async {
    var result = await Services.getCarriers();
    if (result is Success) {
      carrierList = result.response as List<String>;
      notifyListeners();
    } else {
      String errorMessage = result.response as String;
      showSnackBar(errorMessage);
    }
  }

  setAddingTruck(bool loading) async {
    addingTruck = loading;
    notifyListeners();
  }

  setDefaultTrailerType() {
    newTrailerReqModel.type = 'TRAILER';
  }

  isValidNewTrailer() {
    if (newTrailerReqModel.unitNumber.trim().isEmpty) {
      return false;
    }
    return true;
  }

  resetNewTrailerState() {
    selectedClient = null;
    newTrailerReqModel = NewTrailerReqModel(
      carrier: '',
      owner: 'A Blair',
      remarks: '',
      type: 'TRAILER',
      unitNumber: '',
      isHotTrailer: true,
      clientIds: [],
    );
    newTruckAdded = false;
    carrierList = [];
    targetDockIndex = null;
  }

  // Add truck method copied from NewTrailerState
  addTruckToSpecificDock(int dockIndex) async {
    try {
      targetDockIndex = dockIndex;
      setAddingTruck(true);

      // Get selected client
      SpotOnClient? client = await Preferences.getSelectedClient();
      if (client == null) {
        showSnackBar('No client selected', success: false);
        setAddingTruck(false);
        return false;
      }

      newTrailerReqModel.clientIds.clear();
      newTrailerReqModel.clientIds.add(client.clientId!);

      printLog('Adding new trailer to dock $dockIndex: ${newTrailerReqModel.unitNumber}');
      
      var result = await Services.addTruck(newTrailerReqModel);
      
      if (result is Success) {
        String newTrailerUnitNumber = newTrailerReqModel.unitNumber;
        printLog('✅ New trailer ${newTrailerUnitNumber} added successfully');
        
        showSnackBar('Trailer ${newTrailerUnitNumber} added successfully');
        newTruckAdded = true;
        
        // Store unit number before resetting
        String unitNumberToAssign = newTrailerReqModel.unitNumber;
        
        // Reset the model for next use
        resetNewTrailerState();
        setAddingTruck(false);
        
        // Refresh and assign the new trailer to the dock (with retry logic)
        await refreshAndAssignNewTrailer(unitNumberToAssign, dockIndex);
        
        return true; // Success
        
      } else if (result is Failure) {
        newTruckAdded = false;
        String errorMessage = result.response as String;
        printLog('❌ Failed to add new trailer: $errorMessage');
        showSnackBar(errorMessage, success: false);
      }
      
    } catch (e) {
      printLog('ERROR in addTruckToSpecificDock: $e');
      showSnackBar('Error adding trailer: $e', success: false);
      newTruckAdded = false;
    } finally {
      setAddingTruck(false);
      // Reset the model for next use
      resetNewTrailerState();
    }
    
    return false; // Failure
  }

  // Method to refresh and assign new trailer to specific dock
  refreshAndAssignNewTrailer(String newTrailerUnitNumber, int dockIndex) async {
    try {
      printLog('Starting refresh and assignment for new trailer: $newTrailerUnitNumber');
      
      // Add a small delay to ensure the new trailer is available in the backend
      await Future.delayed(const Duration(milliseconds: 500));
      
      // First try to search for the specific trailer
      printLog('Searching for specific new trailer...');
      TruckDetail? newTrailer = await searchSpecificTrailer(newTrailerUnitNumber);
      
      // If not found with specific search, try general refresh with retries
      if (newTrailer == null) {
        printLog('Specific search failed, trying general truck list refresh...');
        
        int retryCount = 0;
        const maxRetries = 3;
        
        while (newTrailer == null && retryCount < maxRetries) {
          retryCount++;
          printLog('Attempt $retryCount: Refreshing truck list...');
          
          await getTruckList();
          
          try {
            newTrailer = truckListModel.list.firstWhere(
              (truck) => truck.unitNumber == newTrailerUnitNumber,
            );
            printLog('Found new trailer in truck list: ${newTrailer.unitNumber}');
          } catch (e) {
            printLog('Attempt $retryCount: New trailer $newTrailerUnitNumber not found in truck list');
            
            if (retryCount < maxRetries) {
              // Wait before next retry
              await Future.delayed(const Duration(milliseconds: 1000));
            }
          }
        }
      }
      
      if (newTrailer == null) {
        printLog('ERROR: New trailer $newTrailerUnitNumber not found after all attempts');
        showSnackBar('New trailer added but not found in list. Please refresh manually.', success: false);
        return;
      }
      
      // Assign the new trailer to the specific dock and mark as edited
      if (dockIndex >= 0 && dockIndex < dockEntries.length) {
        dockEntries[dockIndex].selectedTruckDetail = newTrailer;
        dockEntries[dockIndex].isEdited = true; // Mark as edited when new trailer is assigned
        
        printLog('✅ Successfully assigned new trailer ${newTrailer.unitNumber} to dock ${dockEntries[dockIndex].spot.spotName} and marked as edited');
        showSnackBar('New trailer ${newTrailer.unitNumber} assigned to dock ${dockEntries[dockIndex].spot.spotName}');
        
        // Force UI update immediately after assignment
        notifyListeners();
        
        // Don't call getSpots() here as it would recreate dock entries and overwrite the assignment
        printLog('New trailer assignment completed successfully');
        
      } else {
        printLog('ERROR: Invalid dock index $dockIndex for assigning new trailer');
      }
      
    } catch (e) {
      printLog('ERROR in refreshAndAssignNewTrailer: $e');
      showSnackBar('Error assigning new trailer. Please try again.', success: false);
    }
  }

  // Helper method to get count of edited docks
  int getEditedDocksCount() {
    return dockEntries.where((entry) => entry.isEdited).length;
  }

  // Helper method to get list of edited dock names for debugging
  List<String> getEditedDockNames() {
    return dockEntries
        .where((entry) => entry.isEdited)
        .map((entry) => entry.spot.spotName ?? 'Unknown Dock')
        .toList();
  }

  // Helper method to get count of docks with trailers in transit
  int getTransitDocksCount() {
    return dockEntries.where((entry) => entry.isTransit).length;
  }

  // Helper method to get list of transit dock names for debugging
  List<String> getTransitDockNames() {
    return dockEntries
        .where((entry) => entry.isTransit)
        .map((entry) => entry.spot.spotName ?? 'Unknown Dock')
        .toList();
  }

  // Helper method to check if a specific fleet ID is in transit
  bool isFleetInTransit(String? fleetId) {
    if (fleetId == null) return false;
    return inTransitFleetIds.contains(fleetId);
  }

  init() {}
}
