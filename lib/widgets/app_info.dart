import 'package:flutter/cupertino.dart';
import 'package:package_info/package_info.dart';
// import 'package:package_info_plus/package_info_plus.dart';
import 'package:spot_on/utils/imports.dart';

class AppInfo extends StatelessWidget {
  const AppInfo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      child: FutureBuilder(
        future: getPackageInfo(),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            PackageInfo packageInfo = snapshot.data as PackageInfo;
            return AppTxt(
              text: '${packageInfo.version} (${packageInfo.buildNumber})',
              color: appGrey,
              lines: 2,
              alignment: TextAlign.center,
            );
          }
          return const CupertinoActivityIndicator();
        },
      ),
    );
  }
}
