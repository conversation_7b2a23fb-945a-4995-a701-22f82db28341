import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:spot_on/utils/constants.dart';

class AppLinkBtn extends StatelessWidget {
  //
  final String text;
  final Function onTap;
  final Color? color;
  final Widget? loading;

  const AppLinkBtn({
    Key? key,
    required this.text,
    required this.onTap,
    this.color,
    this.loading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        null == loading
            ? const SizedBox()
            : const Center(child: CupertinoActivityIndicator()),
        Visibility(
          visible: null == loading,
          child: InkWell(
            onTap: () async {
              onTap();
            },
            child: Text(
              text,
              textAlign: TextAlign.center,
              textScaleFactor: 1.0,
              style: TextStyle(
                fontSize: 16,
                height: 1.5,
                fontWeight: FontWeight.normal,
                // decoration: TextDecoration.underline,
                color: color ?? linkColorCode,
              ),
            ),
          ),
        )
      ],
    );
  }
}
