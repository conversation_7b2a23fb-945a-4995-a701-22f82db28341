import 'package:spot_on/providers/create_job_state.dart';
import 'package:spot_on/providers/new_trailer_state.dart';
import 'package:spot_on/utils/imports.dart';

class EntryExitBottomSheetContent extends StatelessWidget {
  const EntryExitBottomSheetContent({
    Key? key,
    required this.isExit,
  }) : super(key: key);

  final bool isExit;

  @override
  Widget build(BuildContext context) {
    CreateJobState createJobState = context.read<CreateJobState>();
    return Container(
      padding: const EdgeInsets.only(top: 20, bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 30, right: 30),
            child: AppTxt(
              text: 'Trailer/Truck ${isExit ? "Exit" : "Entry"}',
              fontSize: 18,
              alignment: TextAlign.center,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 5),
          const Divider(color: Colors.black),
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 5),
                AppTxt(text: 'Locations', color: appGrey),
                const SizedBox(height: 5),
                AppTxt(
                  text:
                      createJobState.selectedEntryExitLocation?.locationName ??
                          '',
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
                const Divider(),
                AppTxt(text: 'Trailer/Truck', color: appGrey),
                const SizedBox(height: 5),
                AppTxt(
                  text: createJobState.selectedTruckDetail?.unitNumber ?? '',
                ),
                const Divider(),
                AppTxt(text: 'Carrier', color: appGrey),
                const SizedBox(height: 5),
                AppTxt(
                  text: createJobState.selectedCarrier ?? '',
                ),
                const Divider(),
                AppTxt(text: 'Notes', color: appGrey),
                const SizedBox(height: 5),
                AppTxt(text: createJobState.entryExitNotes),
              ],
            ),
          ),
          const Divider(color: Colors.black),
          Padding(
            padding: const EdgeInsets.all(20),
            child: AppTxt(
              text: 'Trailer/Truck ${isExit ? "exit" : "entry"} successful',
              fontWeight: FontWeight.bold,
              alignment: TextAlign.center,
              color: appGreen,
              lines: 3,
              lineHeight: 1.2,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 30, right: 30),
            child: AppBtn(
              text: 'DONE',
              bgColor: Theme.of(context).primaryColor,
              color: Colors.white,
              onPress: () async {
                NewTrailerState newTrailerState =
                    context.read<NewTrailerState>();
                closeScreen();
                createJobState.clearSelection();
                createJobState.refresh();
                newTrailerState.resetState();
              },
            ),
          ),
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}
