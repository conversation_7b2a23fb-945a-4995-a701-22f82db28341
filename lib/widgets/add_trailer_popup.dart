import 'package:spot_on/providers/trailer_audit_state.dart';
import 'package:spot_on/utils/imports.dart';

import '../models/cliient_list_model.dart';

class AddTrailerPopup extends StatefulWidget {
  final TrailerAuditState trailerAuditState;
  final int dockIndex;
  final Function(String) onTrailerAdded;

  const AddTrailerPopup({
    Key? key,
    required this.trailerAuditState,
    required this.dockIndex,
    required this.onTrailerAdded,
  }) : super(key: key);

  @override
  State<AddTrailerPopup> createState() => _AddTrailerPopupState();
}

class _AddTrailerPopupState extends State<AddTrailerPopup> {
  final TextEditingController _trailerNumberController =
      TextEditingController();
  final TextEditingController _trailerCarrierController =
      TextEditingController();
  final TextEditingController _trailerTypeController =
      TextEditingController(text: 'Trailer');

  bool _isLoading = false;
  SpotOnClient? _selectedClient;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  // Fixed: Properly handle async initialization
  Future<void> _initializeData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Initialize with default values
      _trailerTypeController.text = 'Trailer';
      widget.trailerAuditState.newTrailerReqModel.type = 'TRAILER';
      widget.trailerAuditState.newTrailerReqModel.owner = 'A Blair';

      // Reset the model to ensure clean state
      widget.trailerAuditState.newTrailerReqModel.unitNumber = '';
      widget.trailerAuditState.newTrailerReqModel.carrier = '';
      widget.trailerAuditState.newTrailerReqModel.remarks = '';

      // Fixed: Properly await the async call
      _selectedClient = await _getClient();
      if (_selectedClient != null) {
        widget.trailerAuditState.newTrailerReqModel.clientIds = [
          _selectedClient!.clientId.toString()
        ];
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error initializing data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _showErrorMessage('Error initializing data: $e');
      }
    }
  }

  // Fixed: Properly defined async method
  Future<SpotOnClient?> _getClient() async {
    try {
      SpotOnClient? spotOnClient = await Preferences.getSelectedClient();
      return spotOnClient;
    } catch (e) {
      print('Error getting client: $e');
      return null;
    }
  }

  // Fixed: Added proper error/success message handling
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showSuccessMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  void dispose() {
    _trailerNumberController.dispose();
    _trailerCarrierController.dispose();
    _trailerTypeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TrailerAuditState>(
      builder: (context, trailerAuditState, child) {
        return Dialog(
          shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 24,
              vertical: 24,
            ),
            child: _isLoading
                ? const Center(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading...'),
                      ],
                    ),
                  )
                : Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      const Text(
                        'Add New Trailer ',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),

                      // Trailer Number Field
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Trailer Number',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            decoration: const BoxDecoration(
                              border: Border(
                                bottom:
                                    BorderSide(color: Colors.grey, width: 1),
                              ),
                            ),
                            child: TextFormField(
                              controller: _trailerNumberController,
                              decoration: const InputDecoration(
                                border: InputBorder.none,
                                contentPadding:
                                    EdgeInsets.symmetric(vertical: 8),
                                hintText: '',
                                hintStyle: TextStyle(color: Colors.grey),
                              ),
                              style: const TextStyle(fontSize: 16),
                              onChanged: (value) {
                                trailerAuditState
                                    .newTrailerReqModel.unitNumber = value;
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),

                      // Carrier Field
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Carrier',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            decoration: const BoxDecoration(
                              border: Border(
                                bottom:
                                    BorderSide(color: Colors.grey, width: 1),
                              ),
                            ),
                            child: TextFormField(
                              controller: _trailerCarrierController,
                              decoration: const InputDecoration(
                                border: InputBorder.none,
                                contentPadding:
                                    EdgeInsets.symmetric(vertical: 8),
                                hintText: '',
                                hintStyle: TextStyle(color: Colors.grey),
                              ),
                              style: const TextStyle(fontSize: 16),
                              onChanged: (value) {
                                trailerAuditState.newTrailerReqModel.carrier =
                                    value;
                              },
                            ),
                          ),
                          // const SizedBox(height: 8),
                          // Container(
                          //   decoration: const BoxDecoration(
                          //     border: Border(
                          //       bottom: BorderSide(color: Colors.grey, width: 1),
                          //     ),
                          //   ),
                          //   child: DropdownButtonFormField<String>(
                          //     decoration: const InputDecoration(
                          //       border: InputBorder.none,
                          //       contentPadding: EdgeInsets.symmetric(vertical: 8),
                          //     ),
                          //     value: trailerAuditState.newTrailerReqModel.carrier.isEmpty
                          //         ? null
                          //         : trailerAuditState.newTrailerReqModel.carrier,
                          //     hint: const Text(
                          //       'Carrier',
                          //       style: TextStyle(color: Colors.grey),
                          //     ),
                          //     isExpanded: true,
                          //     onChanged: (String? selectedCarrier) {
                          //       if (selectedCarrier != null) {
                          //         trailerAuditState.newTrailerReqModel.carrier = selectedCarrier;
                          //         setState(() {});
                          //       }
                          //     },
                          //     items: trailerAuditState.carrierList
                          //         .where((carrier) => carrier.isNotEmpty)
                          //         .toSet()
                          //         .toList()
                          //         .map((String carrier) {
                          //       return DropdownMenuItem<String>(
                          //         value: carrier,
                          //         child: Text(
                          //           carrier,
                          //           style: const TextStyle(fontSize: 16),
                          //         ),
                          //       );
                          //     }).toList(),
                          //   ),
                          // ),
                        ],
                      ),
                      // const SizedBox(height: 20),

                      // // Trailer Type Field
                      // Column(
                      //   crossAxisAlignment: CrossAxisAlignment.start,
                      //   children: [
                      //     const Text(
                      //       'Trailer Type',
                      //       style: TextStyle(
                      //         fontSize: 14,
                      //         color: Colors.grey,
                      //         fontWeight: FontWeight.w500,
                      //       ),
                      //     ),
                      //     const SizedBox(height: 8),
                      //     Container(
                      //       decoration: const BoxDecoration(
                      //         border: Border(
                      //           bottom: BorderSide(color: Colors.grey, width: 1),
                      //         ),
                      //       ),
                      //       child: TextFormField(
                      //         controller: _trailerTypeController,
                      //         enabled: false,
                      //         decoration: const InputDecoration(
                      //           border: InputBorder.none,
                      //           contentPadding: EdgeInsets.symmetric(vertical: 8),
                      //         ),
                      //         style: const TextStyle(
                      //           fontSize: 16,
                      //           color: Colors.black87,
                      //         ),
                      //       ),
                      //     ),
                      //   ],
                      // ),
                      const SizedBox(height: 32),

                      // Buttons
                      Row(
                        children: [
                          // Cancel Button
                          Expanded(
                            child: SizedBox(
                              width: double.infinity,
                              height: 40,
                              child: OutlinedButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                style: OutlinedButton.styleFrom(
                                  side: const BorderSide(color: Colors.grey),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(32),
                                  ),
                                ),
                                child: const Text(
                                  'CANCEL',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          // Save Button
                          Expanded(
                            child: SizedBox(
                              width: double.infinity,
                              height: 40,
                              child: ElevatedButton(
                                onPressed: trailerAuditState.addingTruck
                                    ? null
                                    : () async {
                                        await _handleSave(
                                            context, trailerAuditState);
                                      },
                                style: ElevatedButton.styleFrom(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(32),
                                  ),
                                ),
                                child: trailerAuditState.addingTruck
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor:
                                              AlwaysStoppedAnimation<Color>(
                                                  Colors.white),
                                        ),
                                      )
                                    : const Text(
                                        'SAVE',
                                        style: TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }

  Future<void> _handleSave(
      BuildContext context, TrailerAuditState trailerAuditState) async {
    try {
      // Validation - Fixed: Use proper error handling
      if (!trailerAuditState.isValidNewTrailer()) {
        _showErrorMessage('Please enter Trailer  Number');
        return;
      }
      if (trailerAuditState.selectedClient == null && _selectedClient == null) {
        _showErrorMessage('Please select a client');
        return;
      }
      if (trailerAuditState.newTrailerReqModel.carrier.isEmpty) {
        _showErrorMessage('Please enter a carrier');
        return;
      }

      // Store the unit number before adding
      String newTrailerUnitNumber =
          trailerAuditState.newTrailerReqModel.unitNumber;

      // Show additional loading message
      if (mounted) {
        // ScaffoldMessenger.of(context).showSnackBar(
        //   SnackBar(
        //     content: Text('Adding trailer $newTrailerUnitNumber...'),
        //     duration: const Duration(seconds: 2),
        //   ),
        // );
      }

      // Add truck to specific dock
      bool success =
          await trailerAuditState.addTruckToSpecificDock(widget.dockIndex);

      if (success) {
        _trailerNumberController.clear();
        _trailerCarrierController.clear();
        // Show success message
        if (mounted) {
          // ScaffoldMessenger.of(context).showSnackBar(
          //   SnackBar(
          //     content: Text('Trailer  added  successfully!'),
          //     backgroundColor: Colors.green,
          //     duration: const Duration(seconds: 2),
          //   ),
          // );

          // Small delay to ensure the assignment has completed
          await Future.delayed(const Duration(milliseconds: 300));

          // Close popup after successful assignment
          Navigator.of(context).pop();

          // Then notify parent
          widget.onTrailerAdded(newTrailerUnitNumber);
        }
      } else {
        print('Failed to add trailer. Please try again.');

        // Clear form values even on failure to allow retry with fresh data
        _trailerNumberController.clear();
        _trailerCarrierController.clear();

        // _showErrorMessage('Failed to add trailer. Please try again.');
      }
    } catch (e) {
      print('Error in _handleSave: $e');
      _showErrorMessage('Error adding trailer: $e');
    }
  }
}
