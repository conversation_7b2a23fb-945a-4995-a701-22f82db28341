import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:spot_on/widgets/app_txt.dart';

class AccountHeader extends StatelessWidget {
  const AccountHeader({
    Key? key,
    required this.imgUrl,
    required this.name,
    required this.role,
  }) : super(key: key);

  final String imgUrl;
  final String name;
  final String role;

  final double dimen = 100;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _buildProfileImage(),
        const SizedBox(width: 20),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppTxt(
              text: name,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
            const SizedBox(height: 10),
            AppTxt(text: role),
          ],
        ),
      ],
    );
  }

  Widget _buildProfileImage() {
    bool hasValidUrl = imgUrl.isNotEmpty && 
        !imgUrl.contains('cdn1.iconfinder.com') && 
        Uri.tryParse(imgUrl) != null;

    if (hasValidUrl) {
      return CachedNetworkImage(
        width: dimen,
        height: dimen,
        imageUrl: imgUrl,
        errorWidget: (context, url, error) => _buildDefaultAvatar(),
        imageBuilder: (context, imageProvider) => CircleAvatar(
          radius: dimen / 2,
          backgroundImage: imageProvider,
        ),
        placeholder: (context, url) => _buildLoadingAvatar(),
      );
    } else {
      return _buildDefaultAvatar();
    }
  }

  Widget _buildDefaultAvatar() {
    return CircleAvatar(
      radius: dimen / 2,
      backgroundColor: Colors.grey[300],
      child: Icon(
        Icons.person,
        size: dimen * 0.5,
        color: Colors.grey[600],
      ),
    );
  }

  Widget _buildLoadingAvatar() {
    return Container(
      width: dimen,
      height: dimen,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.grey[200],
      ),
      child: const Center(
        child: CupertinoActivityIndicator(),
      ),
    );
  }
}
