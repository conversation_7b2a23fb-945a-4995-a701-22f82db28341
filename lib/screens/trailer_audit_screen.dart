import 'package:flutter/cupertino.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:spot_on/providers/trailer_audit_state.dart';
import 'package:spot_on/utils/imports.dart';
import 'dart:async';
import '../models/spot_on_locations.dart';
import '../models/truck_list_model.dart';
import '../providers/create_job_state.dart';
import '../widgets/add_trailer_popup.dart';
import '../widgets/transit_confirmation_popup.dart';
import 'trailer_audit_tablet_screen.dart';

class TrailerAuditScreen extends StatefulWidget {
  const TrailerAuditScreen({Key? key}) : super(key: key);

  final String screenTitle = "Trailer Audit";

  @override
  State<TrailerAuditScreen> createState() => _TrailerAuditScreenState();
}

class _TrailerAuditScreenState extends State<TrailerAuditScreen> {
  var controller = TextEditingController();
  late ScrollController _scrollController;
  bool isTablet = false;
  
  // Debounce timer for search
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    print('[TrailerAuditScreen] initState called');
    _scrollController = ScrollController();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      TrailerAuditState trailerAuditState = context.read<TrailerAuditState>();
      print('[TrailerAuditScreen] PostFrameCallback - clearing all and refreshing');
      trailerAuditState.clearAll();
      // trailerAuditState.getTrailerAudit();
      trailerAuditState.getSpotOnLocations();
      trailerAuditState.refresh();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // Helper method to restore scroll position
  void _restoreScrollPosition(double position) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(position);
      }
    });
  }

  // Method to show add trailer popup
  void _showAddTrailerPopup(BuildContext context,
      TrailerAuditState trailerAuditState, int dockIndex) {
    // Store current scroll position before showing popup
    double currentPosition =
        _scrollController.hasClients ? _scrollController.offset : 0.0;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AddTrailerPopup(
          trailerAuditState: trailerAuditState,
          dockIndex: dockIndex,
          onTrailerAdded: (String newTrailerUnitNumber) async {
            // The addTruckToSpecificDock method now handles:
            // 1. Adding the trailer via API
            // 2. Refreshing the truck list
            // 3. Automatically assigning the trailer to the dock
            // 4. Updating the UI
            // Restore scroll position after successful addition
            _restoreScrollPosition(currentPosition);
          },
        );
      },
    );
  }

  // Method to show transit confirmation popup
  void _showTransitConfirmationPopup(
      BuildContext context, TrailerAuditState trailerAuditState) {
    final pendingValidation = trailerAuditState.pendingTransitValidation;
    if (pendingValidation == null) return;

    final truckDetail = pendingValidation['truckDetail'] as TruckDetail;
    final onCancel = pendingValidation['onCancel'] as VoidCallback;
    final onOverride = pendingValidation['onOverride'] as VoidCallback;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return TransitConfirmationPopup(
          trailerUnitNumber: truckDetail.unitNumber ?? 'Unknown',
          onCancel: () {
            Navigator.of(context).pop();
            trailerAuditState.clearPendingTransitValidation();
            onCancel();
          },
          onOverride: () {
            Navigator.of(context).pop();
            trailerAuditState.clearPendingTransitValidation();
            onOverride();
          },
        );
      },
    );
  }

  // Debounced search method for trailers
  Future<List<TruckDetail>> _searchTrailers(String pattern, TrailerAuditState trailerAuditState) async {
    if (pattern.isEmpty) {
      return [];
    }

    // Use a completer to handle the debounced search
    final Completer<List<TruckDetail>> completer = Completer<List<TruckDetail>>();
    
    // Cancel any existing timer
    _debounceTimer?.cancel();
    
    // Set up new timer with 300ms delay
    _debounceTimer = Timer(const Duration(milliseconds: 300), () async {
      try {
        print('[TrailerAuditScreen] Debounced search for pattern: "$pattern"');
        List<TruckDetail> results = await trailerAuditState.searchTrailers(pattern);
        if (!completer.isCompleted) {
          completer.complete(results);
        }
      } catch (e) {
        print('[TrailerAuditScreen] Search error: $e');
        if (!completer.isCompleted) {
          completer.complete([]);
        }
      }
    });
    
    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    TrailerAuditState trailerAuditState = context.watch<TrailerAuditState>();
    var items = trailerAuditState.trailerAudit?.list ?? [];

    print('[TrailerAuditScreen] build() called');
    print('[TrailerAuditScreen] Selected location: ${trailerAuditState.selectedPickUpLocation?.locationName}');
    print('[TrailerAuditScreen] Dock entries count: ${trailerAuditState.dockEntries.length}');
    
    // Log summary of dock entries with data
    if (trailerAuditState.dockEntries.isNotEmpty) {
      int entriesWithData = 0;
      for (int i = 0; i < trailerAuditState.dockEntries.length; i++) {
        var entry = trailerAuditState.dockEntries[i];
        bool hasData = entry.selectedTruckDetail != null || 
                      entry.selectedFleetStatus != null || 
                      entry.trailerController.text.isNotEmpty ||
                      entry.notesController.text.isNotEmpty;
        if (hasData) entriesWithData++;
      }
      print('[TrailerAuditScreen] Entries with prefilled data: $entriesWithData/${trailerAuditState.dockEntries.length}');
    }

    // Check for pending transit validation and show popup
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (trailerAuditState.pendingTransitValidation != null) {
        _showTransitConfirmationPopup(context, trailerAuditState);
      }
    });

    // Check if device is tablet based on width
    isTablet = MediaQuery.of(context).size.width > 600;

    // Show tablet version if screen is wide enough
    if (isTablet) {
      return const TrailerAuditTabletScreen();
    }

    return Scaffold(
      // appBar: AppBar(
      //   title: const Text(
      //     'Trailer Audit',
      //     style: TextStyle(
      //       color: Colors.black,
      //       fontWeight: FontWeight.bold,
      //     ),
      //   ),
      //   backgroundColor: Colors.white,
      //   elevation: 0,
      // ),
      body: SizedBox(
        height: MediaQuery.of(context).size.height,
        child: ModalProgressHUD(
          inAsyncCall: trailerAuditState.isLoading,
          opacity: 0,
          color: Colors.white,
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            color: Colors.white,
            child: Column(
              children: [
                // Location dropdown with underline styling
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.only(
                      left: 0, right: 0, top: 0, bottom: 0),
                  // padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(color: Colors.grey, width: 1),
                          ),
                        ),
                        child: DropdownButtonFormField<SpotOnLocation>(
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            contentPadding: EdgeInsets.symmetric(vertical: 0),
                          ),
                          value: trailerAuditState.selectedPickUpLocation,
                          icon: trailerAuditState.isLoading
                              ? const CupertinoActivityIndicator()
                              : const Icon(Icons.arrow_drop_down),
                          isExpanded: true,
                          hint: const Text('Select Location',
                              style: TextStyle(fontSize: 16)),
                          onChanged: (SpotOnLocation? spotOnLocation) {
                            if (trailerAuditState.isLoading) return;
                            print('[TrailerAuditScreen] Location changed to: ${spotOnLocation?.locationName}');
                            trailerAuditState.selectedPickupSpot = null;
                            trailerAuditState.selectedPickUpLocation =
                                spotOnLocation;
                            trailerAuditState.area =
                                spotOnLocation?.locationName;
                            if (spotOnLocation != null) {
                              print('[TrailerAuditScreen] Getting spots for location ID: ${spotOnLocation.locationId}');
                              trailerAuditState.getSpots(
                                  locationId: spotOnLocation.locationId!,
                                  drop: false);
                            }
                            trailerAuditState.refresh();
                          },
                          items: trailerAuditState.spotOnLocationList.list
                              .map<DropdownMenuItem<SpotOnLocation>>(
                                  (SpotOnLocation value) {
                            return DropdownMenuItem<SpotOnLocation>(
                              value: value,
                              child: Text(value.locationName ?? '',
                                  style: const TextStyle(fontSize: 16)),
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
                ),

                // Dock entries list (when location is selected)
                Expanded(
                  child: trailerAuditState.selectedPickUpLocation != null &&
                          trailerAuditState.dockEntries.isNotEmpty
                      ? ListView.separated(
                          controller: _scrollController,
                          padding: const EdgeInsets.all(0),
                          itemCount: trailerAuditState.dockEntries.length,
                          separatorBuilder: (context, index) => const Divider(
                            height: 32,
                            thickness: 1,
                            color: Colors.grey,
                            indent: 16,
                            endIndent: 16,
                          ),
                          itemBuilder: (context, index) {
                            var dockEntry =
                                trailerAuditState.dockEntries[index];

                            print('[TrailerAuditScreen] Building dock entry $index: Spot="${dockEntry.spot.spotName}" Truck="${dockEntry.selectedTruckDetail?.unitNumber}" Controller="${dockEntry.trailerController.text}" Status="${dockEntry.selectedFleetStatus?.name}" Notes="${dockEntry.notesController.text}" Edited=${dockEntry.isEdited} Counter=${dockEntry.rebuildCounter}');

                            return Container(
                              key: ValueKey(
                                  'dock_entry_${index}_${dockEntry.rebuildCounter}'),
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              // Add alternating background color with border when edited
                              decoration: BoxDecoration(
                                // Use alternating background colors, with green tint if edited
                                color: dockEntry.isEdited
                                    ? Colors.green.shade50
                                    : (index % 2 == 0
                                        ? Colors.grey.shade200
                                        : Colors.white),
                                // Add border when dock has been edited
                                border: dockEntry.isEdited
                                    ? Border.all(
                                        color: Colors.greenAccent, width: 2)
                                    : null,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // First line: Dock, Status, and Trailer in same row with perfectly aligned underlines
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Labels row
                                      Row(
                                        children: [
                                          Expanded(
                                            flex: 1,
                                            child: Row(
                                              children: [
                                                const Text(
                                                  'Dock',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.black,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                // Show an edit indicator next to the dock label
                                                if (dockEntry.isEdited)
                                                  const Padding(
                                                    padding:
                                                        EdgeInsets.only(
                                                            left: 4),
                                                    child: Icon(
                                                      Icons.edit,
                                                      size: 12,
                                                      color: Colors.greenAccent,
                                                    ),
                                                  ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            flex: 1,
                                            child: Row(
                                              children: [
                                                const Text(
                                                  'Trailer',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: Colors.black,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                                const Spacer(),
                                                GestureDetector(
                                                  onTap: () {
                                                    print('[TrailerAuditScreen] Add trailer button tapped for dock $index');
                                                    trailerAuditState
                                                        .setDefaultTrailerType();
                                                    trailerAuditState
                                                        .loadClients();
                                                    trailerAuditState
                                                        .loadCarriers();
                                                    _showAddTrailerPopup(
                                                        context,
                                                        trailerAuditState,
                                                        index);
                                                  },
                                                  child: const Icon(
                                                    Icons.add,
                                                    // color: Theme.of(context).primaryColor,
                                                    color: Colors.green,

                                                    size: 16,
                                                  ),
                                                ),

                                                // Show transit indicator if trailer is in transit
                                                // if (dockEntry.isTransit)
                                                //   Padding(
                                                //     padding: const EdgeInsets.only(left: 8),
                                                //     child: Icon(
                                                //       Icons.local_shipping,
                                                //       size: 14,
                                                //       color: Colors.orange,
                                                //     ),
                                                //   ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          const Expanded(
                                            flex: 1,
                                            child: Text(
                                              'Status',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.black,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 4),

                                      // Fields row with perfectly aligned underlines
                                      IntrinsicHeight(
                                        child: Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.stretch,
                                          children: [
                                            // Dock field
                                            Expanded(
                                              flex: 1,
                                              child: Container(
                                                height:
                                                    48, // Fixed height for alignment
                                                decoration: const BoxDecoration(
                                                  border: Border(
                                                    bottom: BorderSide(
                                                        color: Colors.grey,
                                                        width: 1),
                                                  ),
                                                ),
                                                child: Align(
                                                  alignment:
                                                      Alignment.centerLeft,
                                                  child: Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(vertical: 8),
                                                    child: Text(
                                                      dockEntry.spot.spotName ??
                                                          '',
                                                      style: const TextStyle(
                                                        fontSize: 14,
                                                        fontWeight:
                                                            FontWeight.w500,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 12),

                                            // Trailer field - Updated with debounced search
                                            Expanded(
                                              flex: 1,
                                              child: Container(
                                                height:
                                                    48, // Fixed height for alignment
                                                decoration: const BoxDecoration(
                                                  border: Border(
                                                    bottom: BorderSide(
                                                        color: Colors.grey,
                                                        width: 1),
                                                  ),
                                                ),
                                                child: TypeAheadField<TruckDetail>(
                                                  key: ValueKey(
                                                      'trailer_search_${index}_${dockEntry.selectedTruckDetail?.fleetId ?? 'null'}_${dockEntry.rebuildCounter}'),
                                                  textFieldConfiguration: TextFieldConfiguration(
                                                    controller: dockEntry.trailerController,
                                                    decoration: const InputDecoration(
                                                      border: InputBorder.none,
                                                      contentPadding: EdgeInsets.symmetric(
                                                          vertical: 8, horizontal: 0),
                                                      hintText: 'Trailer',
                                                      hintStyle: TextStyle(fontSize: 14),
                                                    ),
                                                    style: const TextStyle(fontSize: 14),
                                                    onChanged: (text) {
                                                      print('[TrailerAuditScreen] Trailer controller text changed for dock $index: "$text"');
                                                      // Clear selection when user starts typing
                                                      if (dockEntry.selectedTruckDetail != null && 
                                                          text != dockEntry.selectedTruckDetail!.unitNumber) {
                                                        trailerAuditState.updateDockEntry(
                                                          index,
                                                          truckDetail: null,
                                                        );
                                                      }
                                                    },
                                                  ),
                                                  getImmediateSuggestions: false,
                                                  hideOnEmpty: true,
                                                  hideOnLoading: false,
                                                  minCharsForSuggestions: 1,
                                                  suggestionsCallback: (pattern) async {
                                                    print('[TrailerAuditScreen] TypeAhead suggestions for pattern: "$pattern"');
                                                    return await _searchTrailers(pattern, trailerAuditState);
                                                  },
                                                  loadingBuilder: (context) {
                                                    return const Padding(
                                                      padding: EdgeInsets.all(8.0),
                                                      child: Center(
                                                        child: SizedBox(
                                                          height: 16,
                                                          width: 16,
                                                          child: CircularProgressIndicator(strokeWidth: 1),
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                  itemBuilder: (context, suggestion) {
                                                    TruckDetail truck = suggestion;
                                                    return Container(
                                                      padding: const EdgeInsets.all(12.0),
                                                      child: Row(
                                                        children: [
                                                          Expanded(
                                                            child: Text(
                                                              truck.unitNumber ?? '',
                                                              style: const TextStyle(
                                                                fontSize: 14,
                                                                color: Colors.black,
                                                                fontWeight: FontWeight.normal,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    );
                                                  },
                                                  onSuggestionSelected: (TruckDetail truck) {
                                                    print('[TrailerAuditScreen] TypeAhead suggestion selected: ${truck.unitNumber}');
                                                    dockEntry.trailerController.text = truck.unitNumber ?? '';
                                                    trailerAuditState.updateDockEntry(
                                                      index,
                                                      truckDetail: truck,
                                                    );
                                                  },
                                                  noItemsFoundBuilder: (context) {
                                                    return const Padding(
                                                      padding: EdgeInsets.all(12.0),
                                                      child: Text(
                                                        'No trailers found',
                                                        style: TextStyle(color: Colors.grey),
                                                      ),
                                                    );
                                                  },
                                                ),
                                              ),
                                            ),
                                            const SizedBox(width: 12),

                                            // Status field
                                            Expanded(
                                              flex: 1,
                                              child: Container(
                                                height:
                                                    48, // Fixed height for alignment
                                                decoration: const BoxDecoration(
                                                  border: Border(
                                                    bottom: BorderSide(
                                                        color: Colors.grey,
                                                        width: 1),
                                                  ),
                                                ),
                                                child: DropdownButtonFormField<
                                                    FleetStatus>(
                                                  key: ValueKey(
                                                      'status_dropdown_${index}_${dockEntry.selectedFleetStatus?.id ?? 'null'}_${dockEntry.rebuildCounter}'),
                                                  decoration:
                                                      const InputDecoration(
                                                    border: InputBorder.none,
                                                    contentPadding:
                                                        EdgeInsets.symmetric(
                                                            vertical: 8,
                                                            horizontal: 0),
                                                  ),
                                                  value: dockEntry
                                                      .selectedFleetStatus,
                                                  isExpanded: true,
                                                  hint: Text(
                                                      'Status',
                                                      style: TextStyle(
                                                          fontSize: 14,
                                                          color: dockEntry
                                                                      .selectedTruckDetail ==
                                                                  null
                                                              ? Colors
                                                                  .grey.shade400
                                                              : Colors.grey)),
                                                  onChanged: dockEntry
                                                              .selectedTruckDetail ==
                                                          null
                                                      ? null // Disable when no trailer selected
                                                      : (FleetStatus? status) {
                                                          print('[TrailerAuditScreen] Status dropdown changed to: ${status?.name}');
                                                          trailerAuditState
                                                              .updateDockEntry(
                                                            index,
                                                            fleetStatus: status,
                                                          );
                                                        },
                                                  items: trailerAuditState
                                                      .fleetStatuses
                                                      .map<
                                                              DropdownMenuItem<
                                                                  FleetStatus>>(
                                                          (FleetStatus status) {
                                                    return DropdownMenuItem<
                                                        FleetStatus>(
                                                      value: status,
                                                      child: Text(status.name,
                                                          style:
                                                              const TextStyle(
                                                                  fontSize:
                                                                      14)),
                                                    );
                                                  }).toList(),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),

                                  // Second line: Notes and Clear button
                                  Row(
                                    children: [
                                      // Notes section
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              'Notes',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.black,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Container(
                                              decoration: const BoxDecoration(
                                                border: Border(
                                                  bottom: BorderSide(
                                                      color: Colors.grey,
                                                      width: 1),
                                                ),
                                              ),
                                              child: TextFormField(
                                                controller:
                                                    dockEntry.notesController,
                                                decoration:
                                                    const InputDecoration(
                                                  border: InputBorder.none,
                                                  contentPadding:
                                                      EdgeInsets.symmetric(
                                                          vertical: 8),
                                                  hintText: 'Notes',
                                                  hintStyle:
                                                      TextStyle(fontSize: 14),
                                                ),
                                                style: const TextStyle(
                                                    fontSize: 14),
                                                onChanged: (String value) {
                                                  print('[TrailerAuditScreen] Notes changed for dock $index: "$value"');
                                                  trailerAuditState
                                                      .updateDockEntry(
                                                    index,
                                                    notes: value,
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 16),

                                      GestureDetector(
                                        child: const Icon(Icons.close,
                                            color: Colors.red),
                                        onTap: () {
                                          print('[TrailerAuditScreen] Clear button tapped for dock $index');
                                          // Store current scroll position
                                          double currentPosition =
                                              _scrollController.hasClients
                                                  ? _scrollController.offset
                                                  : 0.0;

                                          trailerAuditState.clearDockEntry(
                                            index,
                                            onSuccess: () =>
                                                _restoreScrollPosition(
                                                    currentPosition),
                                          );
                                        },
                                      )

                                      // Padding(
                                      //   padding: const EdgeInsets.only(top: 20),
                                      //   child: TextButton.icon(
                                      //     label: const Text('',
                                      //         style: TextStyle(
                                      //             color: Colors.black,
                                      //             fontSize: 14)),
                                      //     onPressed: () {
                                      //            trailerAuditState.setDefaultTrailerType();
                                      //               trailerAuditState.loadClients();
                                      //               trailerAuditState.loadCarriers();

                                      //               // Show popup instead of navigation
                                      //               _showAddTrailerPopup(context, trailerAuditState, index);
                                      //     },
                                      //     icon:  Icon(Icons.add,
                                      //         color:  Theme.of(context).primaryColor),

                                      //     // label: const Text('Add',
                                      //     //     style: TextStyle(
                                      //     //         color: Colors.black,
                                      //     //         fontSize: 14)),
                                      //   ),
                                      // ),
                                      // // Clear button
                                      // Padding(
                                      //   padding: const EdgeInsets.only(top: 20),
                                      //   child: TextButton.icon(
                                      //     onPressed: () {
                                      //       trailerAuditState
                                      //           .clearDockEntry(index);
                                      //     },
                                      //     icon: const Icon(Icons.close,
                                      //         color: Colors.red, size: 16),
                                      //     label: const Text('',
                                      //         style: TextStyle(
                                      //             color: Colors.red,
                                      //             fontSize: 14)),
                                      //   ),
                                      // ),
                                    ],
                                  ),
                                ],
                              ),
                            );
                          },
                        )
                      : trailerAuditState.selectedPickUpLocation != null
                          ? const Center(
                              child: Text("Please wait Its loading"),
                            )
                          : items.isNotEmpty
                              ? ListView.builder(
                                  padding: const EdgeInsets.all(16),
                                  itemCount: items.length,
                                  itemBuilder: (context, index) {
                                    var item = items[index];
                                    return Card(
                                      margin: const EdgeInsets.only(bottom: 16),
                                      elevation: 1,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.all(16),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text('Location: ${item.area ?? ''}',
                                                style: const TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold)),
                                            const SizedBox(height: 8),
                                            Text('Dock: ${item.slot ?? ''}'),
                                            Text(
                                                'Trailer: ${item.trailerNumber ?? ''}'),
                                            Text(
                                                'Status: ${item.trailerStatus ?? ''}'),
                                            if (item.notes != null &&
                                                item.notes!.isNotEmpty)
                                              Text('Notes: ${item.notes}'),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                )
                              : const Center(
                                  child: Text(
                                      "Select a location to manage dock entries or view all trailer audits!"),
                                ),
                ),

                // Save button - only show when location is selected
                if (trailerAuditState.selectedPickUpLocation != null)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    child: ElevatedButton(
                      onPressed: () {
                        print('[TrailerAuditScreen] Save button pressed');
                        if (trailerAuditState.dockEntries.isNotEmpty) {
                          print('[TrailerAuditScreen] Validating ${trailerAuditState.dockEntries.length} dock entries');
                          // Validate dock entries before saving
                          List<String> missingFields = [];

                          for (int i = 0;
                              i < trailerAuditState.dockEntries.length;
                              i++) {
                            var dockEntry = trailerAuditState.dockEntries[i];
                            String dockName =
                                dockEntry.spot.spotName ?? 'Dock ${i + 1}';

                            print('[TrailerAuditScreen] Validating dock entry $i ($dockName):');
                            print('  - Is edited: ${dockEntry.isEdited}');
                            print('  - Selected truck: ${dockEntry.selectedTruckDetail?.unitNumber}');
                            print('  - Selected status: ${dockEntry.selectedFleetStatus?.name}');
                            print('  - Notes: "${dockEntry.notesController.text}"');

                            // Check only dock entries that have been edited
                            if (dockEntry.isEdited) {
                              // Only validate trailer+status if the user has started filling them
                              // Allow notes-only changes without requiring trailer/status
                              bool hasTrailerOrStatus =
                                  dockEntry.selectedTruckDetail != null ||
                                      dockEntry.selectedFleetStatus != null;

                              print('[TrailerAuditScreen] Has trailer or status: $hasTrailerOrStatus');

                              if (hasTrailerOrStatus) {
                                // If user started adding trailer/status, both are required
                                if (dockEntry.selectedTruckDetail == null) {
                                  missingFields
                                      .add('$dockName: Trailer is required');
                                  print('[TrailerAuditScreen] Missing trailer for $dockName');
                                }
                                if (dockEntry.selectedFleetStatus == null) {
                                  missingFields
                                      .add('$dockName: Status is required');
                                  print('[TrailerAuditScreen] Missing status for $dockName');
                                }
                              }
                            }
                          }

                          print('[TrailerAuditScreen] Missing fields: $missingFields');
                          if (missingFields.isNotEmpty) {
                            // Show validation errors
                            String errorMessage =
                                'Missing required fields:\n\n${missingFields.join('\n')}';
                            print('[TrailerAuditScreen] Showing validation error dialog');
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                return AlertDialog(
                                  title: const Text('Validation Error'),
                                  content: Text(errorMessage),
                                  actions: [
                                    TextButton(
                                      onPressed: () {
                                        Navigator.of(context).pop();
                                      },
                                      child: const Text('OK'),
                                    ),
                                  ],
                                );
                              },
                            );
                          } else {
                            print('[TrailerAuditScreen] All validations passed, proceeding to save');
                            // Store current scroll position before saving
                            double currentPosition =
                                _scrollController.hasClients
                                    ? _scrollController.offset
                                    : 0.0;

                            // Save dock entries when all validations pass
                            trailerAuditState.saveDockEntries(
                              onSuccess: () {
                                print('[TrailerAuditScreen] Save completed successfully');
                                _restoreScrollPosition(currentPosition);
                              },
                            );
                          }
                        } else {
                          print('[TrailerAuditScreen] No dock entries to save');
                          showToast("No dock entries to save!");
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.greenAccent,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'SAVE',
                        style: TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
      backgroundColor: Colors.white,
    );
  }
}
