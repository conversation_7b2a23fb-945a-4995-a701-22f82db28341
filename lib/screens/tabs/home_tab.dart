import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:location/location.dart';
import 'package:spot_on/app_location.dart';
import 'package:spot_on/app_storage.dart';
import 'package:spot_on/models/job/jobs_list_model.dart';
import 'package:spot_on/providers/bol_upload_state.dart';
import 'package:spot_on/providers/create_job_state.dart';
import 'package:spot_on/providers/jobs_state.dart';
import 'package:spot_on/utils/app_logger.dart';
import 'package:spot_on/utils/bol_dvir_status.dart';
import 'package:spot_on/utils/gaps.dart';
import 'package:spot_on/utils/imports.dart';

class HomeTab extends ParentTab {
  const HomeTab({Key? key}) : super(key: key);
  final String screenTitle = 'New Spot';

  @override
  Widget build(BuildContext context) {
    JobsState jobsState = context.watch<JobsState>();
    BolUploadState bolUploadState = context.watch<BolUploadState>();
    print('Home tab len--: ${jobsState.homeJobsListModel.list?.length}');
    return Column(
      children: [
        Visibility(
          visible: jobsState.homeJobsListModel.list!.isEmpty &&
              !jobsState.homeJobsLoading,
          child: Expanded(
            child: InkWell(
              onTap: () async {
                getJobsForHomeTab();
                BolDvirStatus().reset();
                hideSnackBar();
              },
              child: const AppTxt(
                lineHeight: 1.5,
                alignment: TextAlign.center,
                text: 'No spots found\nTap to refresh',
                lines: 2,
              ),
            ),
          ),
        ),
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              getJobsForHomeTab();
              BolDvirStatus().reset();
            },
            child: ListView.builder(
              itemCount: jobsState.homeJobsListModel.list?.length,
              itemBuilder: (context, index) {
                print(
                    'Home tab len----${jobsState.homeJobsListModel.list![index]}');
                Job job = jobsState.homeJobsListModel.list![index];
                return Container(
                  padding: const EdgeInsets.all(0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: getJobStatusColor(job.status!),
                          borderRadius: const BorderRadius.all(
                            Radius.circular(6.0),
                          ),
                        ),
                        padding: const EdgeInsets.all(10),
                        child: Row(
                          children: [
                            Expanded(
                              child: AppTxt(
                                text: '#${job.jobNumber}',
                                color: Colors.white,
                                lines: 2,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(width: 10),
                            AppTxt(
                              text: job.status?.replaceAll('_', ' ') ?? '',
                              fontSize: 12,
                              color: Colors.white,
                            ),
                            const SizedBox(width: 10),
                            AppTxt(
                              text: job.priority?.replaceAll('_', ' ') ?? '',
                              fontSize: 12,
                              color: Colors.white,
                            )
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 10),
                          Row(
                            children: [
                              const Icon(
                                Icons.location_on_outlined,
                                size: 16,
                              ),
                              gapW4,
                              const AppTxt(
                                text: 'Pickup',
                                fontSize: 12,
                                color: appBg,
                                fontWeight: FontWeight.bold,
                              ),
                              const Spacer(),
                              AppTxt(
                                text: job.pickupDateTime ?? '',
                                fontSize: 12,
                                color: appFocusedGrey!,
                              ),
                            ],
                          ),
                          const SizedBox(height: 5),
                          Padding(
                            padding: const EdgeInsets.only(left: 20.0),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      AppTxt(
                                        text:
                                            '${job.pickupLocation?.locationName}',
                                      ),
                                      const SizedBox(height: 5),
                                      AppTxt(
                                          text:
                                              '${job.pickupLocation?.street}'),
                                      const SizedBox(height: 5),
                                      AppTxt(
                                        text:
                                            '${job.pickupLocation?.city}, ${job.pickupLocation?.zip}',
                                      ),
                                      const SizedBox(height: 5),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Container(
                                  width: 88,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(6),
                                    color: Colors.grey[300],
                                  ),
                                  alignment: Alignment.center,
                                  padding:
                                      const EdgeInsets.fromLTRB(10, 10, 10, 10),
                                  child: AppTxt(
                                    text: '${job.pickupSpot?.spotName}',
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 5),
                        ],
                      ),
                      gapH12,
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 5),
                          Row(
                            children: [
                              const Icon(
                                Icons.location_on_outlined,
                                size: 16,
                              ),
                              gapW4,
                              const AppTxt(
                                text: 'Drop',
                                fontSize: 12,
                                color: appBg,
                                fontWeight: FontWeight.bold,
                              ),
                              AppTxt(
                                text: job.dropDateTime ?? '',
                                fontSize: 12,
                                color: appFocusedGrey!,
                              ),
                            ],
                          ),
                          const SizedBox(height: 5),
                          Padding(
                              padding: const EdgeInsets.only(left: 20.0),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        AppTxt(
                                          text:
                                              job.dropLocation?.locationName ??
                                                  '-',
                                        ),
                                        const SizedBox(height: 5),
                                        AppTxt(
                                            text:
                                                '${job.dropLocation?.street}'),
                                        const SizedBox(height: 5),
                                        AppTxt(
                                            text:
                                                '${job.dropLocation?.city}, ${job.dropLocation?.zip}'),
                                        const SizedBox(height: 5),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Container(
                                    width: 88,
                                    alignment: Alignment.center,
                                    padding: const EdgeInsets.fromLTRB(
                                        10, 10, 10, 10),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(6),
                                      color: Colors.grey[300],
                                    ),
                                    child: AppTxt(
                                      text: job.dropSpot?.spotName ?? '-',
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              )),
                          const SizedBox(height: 5),
                        ],
                      ),
                      gapH12,
                      const Row(
                        children: [
                          Icon(
                            Icons.description_outlined,
                            size: 16,
                          ),
                          gapW4,
                          AppTxt(
                            text: 'Spot Notes',
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Padding(
                        padding: const EdgeInsets.only(left: 20.0),
                        child: AppTxt(
                          text: Utils.buildJobDetails(job),
                          lines: 12,
                          lineHeight: 1.2,
                        ),
                      ),
                      gapH24,
                      const Row(
                        children: [
                          Icon(
                            Icons.description_outlined,
                            size: 16,
                          ),
                          gapW4,
                          AppTxt(
                            text: 'Trailer',
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 20.0, top: 8),
                        child: AppTxt(text: job.fleet?.unitNumber ?? ''),
                      ),
                      // const Divider(),
                      gapH24,
                      const Row(
                        children: [
                          Icon(
                            Icons.description_outlined,
                            size: 16,
                          ),
                          gapW4,
                          AppTxt(
                            text: 'Assigned By',
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 20.0, top: 8),
                        child: Row(
                          children: [
                            AppTxt(
                              text:
                                  '${job.audit?.createdBy!.firstName} ${job.audit?.createdBy?.lastName}',
                            ),
                            const Spacer(),
                            AppTxt(
                              text: job.audit!.createdDate!,
                              fontSize: 12,
                              color: appFocusedGrey!,
                            )
                          ],
                        ),
                      ),
                      isBolEnabled() &&
                              isDriver() &&
                              (job.bols?.isNotEmpty ?? false)
                          ? Column(
                              children: [
                                gapH16,
                                const Row(
                                  children: [
                                    Icon(
                                      Icons.image_outlined,
                                      size: 16,
                                    ),
                                    gapW4,
                                    AppTxt(
                                      text: 'BoLs',
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ],
                                ),
                                Padding(
                                  padding:
                                      const EdgeInsets.only(left: 20.0, top: 8),
                                  child: SizedBox(
                                    height: 56,
                                    child: ListView.separated(
                                      itemBuilder: (context, index) {
                                        var images = job.bols;
                                        var image =
                                            images?[index].imagePath ?? "";
                                        return GestureDetector(
                                          onTap: () {
                                            showImageViewer(
                                              context,
                                              Image.network(image).image,
                                              swipeDismissible: true,
                                              doubleTapZoomable: true,
                                              backgroundColor: Colors.black87,
                                            );
                                          },
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(4),
                                            child: Image.network(
                                              image,
                                              height: 56,
                                              width: 56,
                                              fit: BoxFit.cover,
                                            ),
                                          ),
                                        );
                                      },
                                      scrollDirection: Axis.horizontal,
                                      separatorBuilder:
                                          (BuildContext context, int index) {
                                        return gapW12;
                                      },
                                      itemCount: job.bols?.length ?? 0,
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : const SizedBox.shrink(),
                      gapH24,
                      Padding(
                        padding: const EdgeInsets.only(left: 0, right: 0),
                        child: AppBtn(
                          color: Colors.white,
                          loading: jobsState.jobUpdating,
                          borderColor:
                              getHomeJobActionBtnColor(context, job.status!),
                          bgColor:
                              getHomeJobActionBtnColor(context, job.status!),
                          text: getHomeJobActionBtnText(job.status!),
                          onPress: () async {
                            print('clicked');
                            AppStorage.clear();
                            bolUploadState.clearImages();
                            bool isPickUp = job.status! == 'OPEN';

                            CreateJobState createJobState =
                                context.read<CreateJobState>();
                            createJobState.selectedJob = job;
                            createJobState.selectedDropSpot = null;
                            createJobState.selectedPickupSpot = null;
                            job.pickupSpot;

                            if (!isPickUp && null != job.dropLocation) {
                              var clientId = job.dropLocation?.clientId;
                              createJobState.getDropOffLocations(
                                  clientId ?? '', job.assignedTo?.userId ?? '');

                              createJobState.getSpots(
                                locationId: job.dropLocation!.locationId!,
                                drop: !isPickUp,
                              );

                              showTripUpdateBottomSheetDropOff(
                                job: job,
                                isPickUp: isPickUp,
                                onClose: () async {
                                  createJobState.selectedDropLocation = null;
                                  createJobState.refresh();
                                  JobsState jobsState =
                                      context.read<JobsState>();
                                  await jobsState.getHomeJobs();
                                  jobsState.getJobs();
                                },
                                onTextChange: (val) async {
                                  jobsState.notes = val;
                                },
                                onConfirmTap: () async {
                                  job.jobUpdateStatus =
                                      getJobUpdateStatusString(job.status!);
                                  jobsState.doJobUpdate(
                                    job,
                                    jobsState.notes,
                                    DateTime.now(),
                                  );
                                  closeScreen();
                                  jobsState.notes = "";
                                },
                              );
                            } else if (isPickUp && null != job.pickupLocation) {
                              createJobState.getSpots(
                                locationId: job.pickupLocation!.locationId!,
                                drop: !isPickUp,
                              );
                              if (!await AppLocation().isLocationEnabled()) {
                                return;
                              }
                              if (!await AppLocation().isLocationGranted()) {
                                return;
                              }
                              showTripUpdateBottomSheetPickUp(
                                job: job,
                                isPickUp: isPickUp,
                                onClose: () async {
                                  JobsState jobsState =
                                      context.read<JobsState>();
                                  await jobsState.getHomeJobs();
                                  jobsState.getJobs();
                                },
                                onTextChange: (val) async {
                                  jobsState.notes = val;
                                },
                                onConfirmTap: () async {
                                  LocationData? location;
                                  try {
                                    location =
                                        await AppLocation().getLocation();
                                  } catch (e) {
                                    location = null;
                                    printLog(e.toString());
                                  }
                                  job.jobUpdateStatus =
                                      getJobUpdateStatusString(job.status!);
                                  jobsState.doJobUpdate(
                                    job,
                                    jobsState.notes,
                                    DateTime.now(),
                                    lat: location?.latitude.toString(),
                                    lng: location?.longitude.toString(),
                                  );
                                  closeScreen();
                                  jobsState.notes = "";
                                },
                              );
                            }
                          },
                        ),
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
