import 'package:spot_on/models/cliient_list_model.dart';
import 'package:spot_on/models/user_info_model.dart';
import 'package:spot_on/providers/create_job_state.dart';
import 'package:spot_on/providers/jobs_state.dart';
import 'package:spot_on/providers/new_trailer_state.dart';
import 'package:spot_on/screens/web_view_screen.dart';
import 'package:spot_on/utils/bol_dvir_status.dart';
import 'package:spot_on/utils/imports.dart';
import 'package:spot_on/widgets/app_info.dart';

class AccountTab extends ParentTab {
  const AccountTab({Key? key}) : super(key: key);
  final String screenTitle = 'Account';

  @override
  Widget build(BuildContext context) {
    HomeState homeState = context.watch<HomeState>();
    UserInfoModel? userInfoModel = homeState.userInfoModel;
    LoginState loginState = context.watch<LoginState>();
    if (null == userInfoModel) {
      return const SizedBox();
    }
    return ListView(
      shrinkWrap: true,
      children: [
        AccountHeader(
          imgUrl: '', // Use empty string to trigger default avatar
          name: '${userInfoModel.firstName} ${userInfoModel.lastName}',
          role: userInfoModel.roles.first.roleName ?? '',
        ),
        const SizedBox(height: 30),
        const Row(
          children: [
            AppTxt(
              text: 'Personal Details',
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
            Spacer(),
          ],
        ),
        const SizedBox(height: 20),
        AppListTile(
          title: 'First Name',
          value: userInfoModel.firstName ?? '',
          editMode: homeState.profileEditMode,
          onTextUpdate: (val) async {
            homeState.profileUpdateModel.firstName = val;
          },
        ),
        const SizedBox(height: 20),
        AppListTile(
          title: 'Last Name',
          value: userInfoModel.lastName ?? '',
          editMode: homeState.profileEditMode,
          onTextUpdate: (val) async {
            homeState.profileUpdateModel.lastName = val;
          },
        ),
        const SizedBox(height: 20),
        AppListTile1(
          title: 'Mobile Number',
          value: userInfoModel.phone ?? '',
          editMode: homeState.profileEditMode,
          onTextUpdate: (val) async {
            homeState.profileUpdateModel.phone = val;
          },
        ),
        const SizedBox(height: 20),
        AppListTile(
          title: 'Email Address',
          editMode: homeState.profileEditMode,
          value: userInfoModel.email ?? '',
          onTextUpdate: (val) async {
            homeState.profileUpdateModel.email = val;
          },
        ),
        const SizedBox(height: 20),
        AppListTile(
          title: 'Password',
          value: homeState.profileEditMode
              ? homeState.profileUpdateModel.newPassword
              : '******',
          editMode: homeState.profileEditMode,
          icon: const Icon(Icons.remove_red_eye),
          onTextUpdate: (val) async {
            homeState.profileUpdateModel.newPassword = val;
          },
        ),
        const SizedBox(height: 10),
        Visibility(
          visible: homeState.profileEditMode,
          child: AppTxt(
            text: 'Keep it empty if you do not want to change password.',
            color: appGrey,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 20),
        AppBtn(
          color: Colors.white,
          loading: homeState.profileUpdating,
          bgColor: Theme.of(context).primaryColor,
          text: homeState.profileEditMode ? 'SAVE' : 'EDIT',
          onPress: () async {
            if (homeState.profileUpdating) {
              return;
            }
            if (homeState.profileEditMode) {
              homeState.updateUserInfo();
            } else {
              homeState.profileUpdateModel.firstName =
                  homeState.userInfoModel!.firstName!;
              homeState.profileUpdateModel.lastName =
                  homeState.userInfoModel!.lastName!;
              //commented
              // homeState.profileUpdateModel.phone = homeState.userInfoModel!.phone!;
              homeState.profileUpdateModel.email =
                  homeState.userInfoModel!.email!;
              homeState.profileUpdateModel.newPassword = '';
            }
            homeState.profileEditMode = !homeState.profileEditMode;
            homeState.refresh();
          },
        ),
        const SizedBox(height: 30),
        const AppTxt(
          text: 'Select Client',
          fontWeight: FontWeight.bold,
        ),
        Column(
          children: loginState.clientListModel.list.map((client) {
            return InkWell(
              onTap: () async {
                loginState.selectedClient = client;
                for (SpotOnClient c in loginState.clientListModel.list) {
                  c.selected = c.clientId == client.clientId;
                }
                loginState.refresh();
                await Preferences.saveSelectedClient(client);
                BolDvirStatus().reset();
              },
              child: Container(
                padding: const EdgeInsets.fromLTRB(0, 10, 0, 10),
                child: Row(
                  children: [
                    const Icon(Icons.person),
                    const SizedBox(width: 20),
                    AppTxt(
                      text: client.clientName ?? '',
                      fontWeight:
                          client.selected ? FontWeight.bold : FontWeight.normal,
                      color: client.selected
                          ? appGreen
                          : Theme.of(context).primaryColor,
                      fontSize: 18,
                      lines: 1,
                    ),
                    const Spacer(),
                    Visibility(
                      visible: client.selected,
                      child: const Icon(Icons.check),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 30),
        AppBtn(
          color: Colors.white,
          bgColor: Theme.of(context).primaryColor,
          text: 'LOGOUT',
          onPress: () async {
            hideSnackBar();
            LoginState loginState = context.read<LoginState>();
            HomeState homeState = context.read<HomeState>();
            CreateJobState createJobState = context.read<CreateJobState>();
            NewTrailerState newTrailerState =
                globalKey.currentContext!.read<NewTrailerState>();
            JobsState jobsState = context.read<JobsState>();
            createJobState.clearSelection();
            createJobState.clearAllLists();
            jobsState.homeJobsListModel.list!.clear();
            jobsState.jobsListModel.list!.clear();
            loginState.doLogout();
            Preferences.removeLoginInfo();
            Preferences.removeSelectedClient();
            homeState.userInfoModel = null;
            homeState.currentTabIndex = 0;
            newTrailerState.resetState();
            openLogin();
          },
        ),
        const SizedBox(height: 16),
        TextButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => WebViewScreen(
                  url: Services.termsUrl,
                  title: "Terms & Conditions",
                ),
              ),
            );
          },
          child: const Text(
            "Terms & Conditions",
            style: TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (_) => WebViewScreen(
                  url: Services.privacyUrl,
                  title: "Privacy Policy",
                ),
              ),
            );
          },
          child: const Text(
            "Privacy Policy",
            style: TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(height: 24),
        const Center(child: AppInfo()),
      ],
    );
  }
}
