import 'package:easy_image_viewer/easy_image_viewer.dart';
import 'package:spot_on/models/job/jobs_list_model.dart';
import 'package:spot_on/providers/jobs_state.dart';
import 'package:spot_on/utils/imports.dart';

import '../../utils/gaps.dart';

class MyJobsTab extends ParentTab {
  const MyJobsTab({Key? key}) : super(key: key);

  final String screenTitle = 'My Spots';

  @override
  Widget build(BuildContext context) {
    JobsState jobsState = context.watch<JobsState>();
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        toolbarHeight: 0,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppTxt(text: 'Today (${jobsState.jobsCount})'),
          const SizedBox(height: 20),
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                JobsState jobsState =
                    globalKey.currentContext!.read<JobsState>();
                jobsState.getJobs();
              },
              child: ListView.separated(
                itemCount: jobsState.jobsListModel.list!.length,
                itemBuilder: (context, index) {
                  Job job = jobsState.jobsListModel.list![index];
                  if (isDriver() || isSpotter()) {
                    if (job.dropDateTime != null &&
                        isTodayJob(job.dropDateTime!)) {
                      return Container(
                        padding: const EdgeInsets.all(0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(10),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6),
                                color: getJobStatusColor(job.status!),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: AppTxt(
                                      text: '#${job.jobNumber}',
                                      color: Colors.white,
                                      lines: 2,
                                      fontSize: 15,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(width: 20),
                                  AppTxt(
                                    text: '${job.status}',
                                    color: Colors.white,
                                    fontSize: 12,
                                  )
                                ],
                              ),
                            ),
                            const SizedBox(height: 10),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 5),
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.location_on_outlined,
                                      size: 16,
                                    ),
                                    gapW4,
                                    const AppTxt(
                                      text: 'Pickup',
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    const Spacer(),
                                    AppTxt(
                                      text: job.pickupDateTime ?? '',
                                      fontSize: 12,
                                      color: appFocusedGrey!,
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 5),
                                Padding(
                                  padding: const EdgeInsets.only(left: 20.0),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            AppTxt(
                                                text:
                                                    '${job.pickupLocation?.locationName}'),
                                            const SizedBox(height: 5),
                                            AppTxt(
                                                text:
                                                    '${job.pickupLocation?.street}'),
                                            const SizedBox(height: 5),
                                            AppTxt(
                                                text:
                                                    '${job.pickupLocation?.city}, ${job.pickupLocation?.zip}'),
                                            const SizedBox(height: 5),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                      Container(
                                        width: 88,
                                        alignment: Alignment.center,
                                        padding: const EdgeInsets.fromLTRB(
                                            10, 10, 10, 10),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          color: Colors.grey[300],
                                        ),
                                        child: AppTxt(
                                          text: '${job.pickupSpot?.spotName}',
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 5),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(height: 5),
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.location_on_outlined,
                                      size: 16,
                                    ),
                                    gapW4,
                                    const AppTxt(
                                      text: 'Drop',
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    const Spacer(),
                                    AppTxt(
                                      text: job.dropDateTime ?? '',
                                      fontSize: 12,
                                      color: appFocusedGrey!,
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 5),
                                Padding(
                                  padding: const EdgeInsets.only(left: 20.0),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            const SizedBox(height: 5),
                                            AppTxt(
                                                text:
                                                    '${job.dropLocation?.locationName}'),
                                            const SizedBox(height: 5),
                                            AppTxt(
                                                text:
                                                    '${job.dropLocation?.street}'),
                                            const SizedBox(height: 5),
                                            AppTxt(
                                                text:
                                                    '${job.dropLocation?.city}, ${job.dropLocation?.zip}'),
                                            const SizedBox(height: 5),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 10),
                                      Container(
                                        width: 88,
                                        alignment: Alignment.center,
                                        padding: const EdgeInsets.fromLTRB(
                                            10, 10, 10, 10),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          color: Colors.grey[300],
                                        ),
                                        child: AppTxt(
                                          text: job.dropSpot?.spotName ?? '-',
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(height: 5),
                              ],
                            ),
                            const SizedBox(height: 10),
                            const Row(
                              children: [
                                Icon(
                                  Icons.description_outlined,
                                  size: 16,
                                ),
                                gapW4,
                                AppTxt(
                                  text: 'Spot Notes',
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),
                            Padding(
                              padding: const EdgeInsets.only(left: 20.0),
                              child: AppTxt(
                                text: Utils.buildJobDetails(job),
                                lines: 12,
                                lineHeight: 1.2,
                              ),
                            ),
                            gapH16,
                            const Row(
                              children: [
                                Icon(
                                  Icons.description_outlined,
                                  size: 16,
                                ),
                                gapW4,
                                AppTxt(
                                  text: 'Trailer',
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ],
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.only(left: 20.0, top: 8),
                              child: AppTxt(text: job.fleet?.unitNumber ?? ''),
                            ),
                            // const Divider(),
                            gapH16,
                            const Row(
                              children: [
                                Icon(
                                  Icons.description_outlined,
                                  size: 16,
                                ),
                                gapW4,
                                AppTxt(
                                  text: 'Assigned By',
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ],
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.only(left: 20.0, top: 8),
                              child: Row(
                                children: [
                                  AppTxt(
                                    text:
                                        '${job.audit?.createdBy!.firstName} ${job.audit?.createdBy?.lastName}',
                                  ),
                                  const Spacer(),
                                  AppTxt(
                                    text: job.audit!.createdDate!,
                                    fontSize: 12,
                                    color: appFocusedGrey!,
                                  )
                                ],
                              ),
                            ),
                            isBolEnabled() &&
                                    isDriver() &&
                                    (job.bols?.isNotEmpty ?? false)
                                ? Column(
                                    children: [
                                      gapH16,
                                      const Row(
                                        children: [
                                          Icon(
                                            Icons.image_outlined,
                                            size: 16,
                                          ),
                                          gapW4,
                                          AppTxt(
                                            text: 'BoLs',
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ],
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            left: 20.0, top: 8),
                                        child: SizedBox(
                                          height: 56,
                                          child: ListView.separated(
                                            itemBuilder: (context, index) {
                                              var images = job.bols;
                                              var image =
                                                  images?[index].imagePath ??
                                                      "";
                                              return GestureDetector(
                                                onTap: () {
                                                  showImageViewer(
                                                    context,
                                                    Image.network(image).image,
                                                    swipeDismissible: true,
                                                    doubleTapZoomable: true,
                                                    backgroundColor:
                                                        Colors.black87,
                                                  );
                                                },
                                                child: ClipRRect(
                                                  borderRadius:
                                                      BorderRadius.circular(4),
                                                  child: Image.network(
                                                    image,
                                                    height: 56,
                                                    width: 56,
                                                    fit: BoxFit.cover,
                                                  ),
                                                ),
                                              );
                                            },
                                            scrollDirection: Axis.horizontal,
                                            separatorBuilder:
                                                (BuildContext context,
                                                    int index) {
                                              return gapW12;
                                            },
                                            itemCount: job.bols?.length ?? 0,
                                          ),
                                        ),
                                      ),
                                    ],
                                  )
                                : const SizedBox.shrink(),
                            const SizedBox(height: 30),
                          ],
                        ),
                      );
                    } else {
                      return const SizedBox();
                    }
                  } else {
                    return Container(
                      padding: const EdgeInsets.all(0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            color: getJobStatusColor(job.status!),
                            padding: const EdgeInsets.all(10),
                            child: Row(
                              children: [
                                Expanded(
                                  child: AppTxt(
                                    text: '#${job.jobNumber}',
                                    color: Colors.white,
                                    lines: 2,
                                    fontSize: 15,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(width: 20),
                                AppTxt(
                                  text: '${job.status}',
                                  color: Colors.white,
                                  fontSize: 12,
                                )
                              ],
                            ),
                          ),
                          const SizedBox(height: 10),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 5),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.location_pin,
                                    size: 20,
                                  ),
                                  AppTxt(
                                    text: 'Pickup',
                                    fontSize: 12,
                                    color: appFocusedGrey!,
                                  ),
                                  const Spacer(),
                                  AppTxt(
                                    text: job.pickupDateTime ?? '',
                                    fontSize: 12,
                                    color: appFocusedGrey!,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 5),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          AppTxt(
                                              text:
                                                  '${job.pickupLocation?.locationName}'),
                                          const SizedBox(height: 5),
                                          AppTxt(
                                              text:
                                                  '${job.pickupLocation?.street}'),
                                          const SizedBox(height: 5),
                                          AppTxt(
                                              text:
                                                  '${job.pickupLocation?.city}, ${job.pickupLocation?.zip}'),
                                          const SizedBox(height: 5),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    Container(
                                      width: 88,
                                      alignment: Alignment.center,
                                      padding: const EdgeInsets.fromLTRB(
                                          10, 10, 10, 10),
                                      color: Colors.grey[300],
                                      child: AppTxt(
                                        text: '${job.pickupSpot?.spotName}',
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 5),
                            ],
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 5),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.location_pin,
                                    size: 20,
                                  ),
                                  const AppTxt(
                                    text: 'Drop',
                                    fontSize: 12,
                                  ),
                                  const Spacer(),
                                  AppTxt(
                                    text: job.dropDateTime ?? '',
                                    fontSize: 12,
                                    color: appFocusedGrey!,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 5),
                              Padding(
                                padding: const EdgeInsets.only(left: 20.0),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          const SizedBox(height: 5),
                                          AppTxt(
                                              text:
                                                  '${job.dropLocation?.locationName}'),
                                          const SizedBox(height: 5),
                                          AppTxt(
                                              text:
                                                  '${job.dropLocation?.street}'),
                                          const SizedBox(height: 5),
                                          AppTxt(
                                              text:
                                                  '${job.dropLocation?.city}, ${job.dropLocation?.zip}'),
                                          const SizedBox(height: 5),
                                        ],
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    Container(
                                      width: 88,
                                      alignment: Alignment.center,
                                      padding: const EdgeInsets.fromLTRB(
                                          10, 10, 10, 10),
                                      color: Colors.grey[300],
                                      child: AppTxt(
                                        text: job.dropSpot?.spotName ?? '-',
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(height: 5),
                            ],
                          ),
                          const SizedBox(height: 10),
                          AppTxt(text: job.description ?? ''),
                          const Divider(),
                          Row(
                            children: [
                              const AppTxt(text: 'Trailer: '),
                              AppTxt(text: job.fleet?.unitNumber ?? ''),
                            ],
                          ),
                          const Divider(),
                          Row(
                            children: [
                              const AppTxt(
                                text: 'Assigned by: ',
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                              Expanded(
                                child: AppTxt(
                                  text:
                                      '${job.audit?.createdBy!.firstName} ${job.audit?.createdBy?.lastName}',
                                ),
                              ),
                              AppTxt(
                                text: job.audit!.createdDate ?? '',
                                fontSize: 12,
                                color: appFocusedGrey!,
                              ),
                            ],
                          ),
                          const SizedBox(height: 30),
                        ],
                      ),
                    );
                  }
                },
                separatorBuilder: (BuildContext context, int index) {
                  return const SizedBox.shrink();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
