import 'package:spot_on/models/cliient_list_model.dart';
import 'package:spot_on/providers/trailer_audit_state.dart';
import 'package:spot_on/utils/gaps.dart';
import 'package:spot_on/utils/imports.dart';

class AddNewTrailerForDock extends StatefulWidget {
  final int dockIndex;

  const AddNewTrailerForDock({Key? key, required this.dockIndex})
      : super(key: key);

  @override
  State<AddNewTrailerForDock> createState() => _AddNewTrailerForDockState();
}

class _AddNewTrailerForDockState extends State<AddNewTrailerForDock> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      TrailerAuditState trailerAuditState = context.read<TrailerAuditState>();
      trailerAuditState.newTrailerReqModel.carrier = '';
      trailerAuditState.loadCarriers();
    });
  }

  @override
  Widget build(BuildContext context) {
    TrailerAuditState trailerAuditState = context.watch<TrailerAuditState>();

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const AppTxt(
          text: 'Add New Trailer',
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: appGrey),
          onPressed: () async {
            Utils.hideKeyboard(globalKey.currentContext!);
            hideSnackBar();
            closeScreen();
          },
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(20),
        children: [
          AppTF(
            hintText: 'Unit Number *',
            onTextChanged: (val) async {
              trailerAuditState.newTrailerReqModel.unitNumber = val;
            },
          ),
          gapH12,
          AppTF(
            hintText: 'Owner',
            initialValue: "A Blair",
            enabled: false,
            textStyle: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
            onTextChanged: (val) async {
              // Owner is fixed
            },
          ),
          gapH24,
          const AppTxt(
            text: 'Select Client',
            fontWeight: FontWeight.bold,
          ),
          DropdownButton<SpotOnClient>(
            value: trailerAuditState.selectedClient,
            icon: const Icon(Icons.arrow_drop_down_outlined),
            iconSize: 24,
            hint: const Padding(
              padding: EdgeInsets.only(
                left: 16,
                right: 16,
              ),
              child: AppTxt(text: 'Select Client'),
            ),
            elevation: 16,
            isExpanded: true,
            style: TextStyle(color: Theme.of(context).primaryColor),
            onChanged: (SpotOnClient? selectedClient) {
              if (selectedClient == null) {
                return;
              }
              trailerAuditState.selectedClient = selectedClient;
              trailerAuditState.refresh();
            },
            items: trailerAuditState.clientListModel.list
                .map<DropdownMenuItem<SpotOnClient>>((SpotOnClient client) {
              return DropdownMenuItem<SpotOnClient>(
                value: client,
                child: AppTxt(
                  text: client.clientName ?? '',
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              );
            }).toList(),
          ),
          gapH6,
          const AppTxt(
            text: 'Select Carrier',
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
          gapH4,
          DropdownButton<String>(
            value: trailerAuditState.newTrailerReqModel.carrier.isEmpty
                ? null
                : trailerAuditState.newTrailerReqModel.carrier,
            icon: const Icon(Icons.arrow_drop_down_outlined),
            iconSize: 24,
            hint: const AppTxt(text: 'Select Carrier'),
            elevation: 16,
            isExpanded: true,
            style: TextStyle(color: Theme.of(context).primaryColor),
            onChanged: (String? selectedCarrier) {
              if (selectedCarrier == null) return;
              trailerAuditState.newTrailerReqModel.carrier = selectedCarrier;
              trailerAuditState.refresh();
            },
            items: trailerAuditState.carrierList
                .where((carrier) => carrier.isNotEmpty)
                .toSet()
                .toList()
                .map((String carrier) {
              return DropdownMenuItem<String>(
                value: carrier,
                child: AppTxt(
                  text: carrier,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              );
            }).toList(),
          ),
          AppTF(
            hintText: 'Remarks',
            onTextChanged: (val) async {
              trailerAuditState.newTrailerReqModel.remarks = val;
            },
          ),
          gapH32,
          AppBtn(
            loading: trailerAuditState.addingTruck,
            bgColor: Theme.of(context).primaryColor,
            color: Colors.white,
            text: 'Add',
            onPress: () async {
              if (!trailerAuditState.isValidNewTrailer()) {
                showSnackBar('Please enter Trailer Number', success: false);
                return;
              }
              if (null == trailerAuditState.selectedClient) {
                showSnackBar('Please select a client', success: false);
                return;
              }
              if (trailerAuditState.newTrailerReqModel.carrier.isEmpty) {
                showSnackBar('Please add a carrier', success: false);
                return;
              }

              // Store the unit number and navigator before adding
              String newTrailerUnitNumber =
                  trailerAuditState.newTrailerReqModel.unitNumber;
              NavigatorState navigator = Navigator.of(context);

              // Add truck to specific dock
              bool success = await trailerAuditState
                  .addTruckToSpecificDock(widget.dockIndex);

              if (success) {
                // Return the new trailer unit number to the calling screen
                navigator.pop(newTrailerUnitNumber);
              }
            },
          ),
        ],
      ),
    );
  }
}
