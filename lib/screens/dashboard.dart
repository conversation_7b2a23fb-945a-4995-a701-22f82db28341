import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:spot_on/providers/create_job_state.dart';
import 'package:spot_on/providers/jobs_state.dart';
import 'package:spot_on/providers/new_trailer_state.dart';
import 'package:spot_on/screens/tabs/account_tab.dart';
import 'package:spot_on/screens/tabs/home_tab.dart';
import 'package:spot_on/screens/tabs/my_jobs_tab.dart';
import 'package:spot_on/screens/trailer_audit_screen.dart';
import 'package:spot_on/utils/imports.dart';

import 'Damage_req_screen.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    HomeState homeState = context.watch<HomeState>();
    JobsState jobsState = context.watch<JobsState>();
    CreateJobState createJobState = context.watch<CreateJobState>();
    return Scaffold(
      backgroundColor: appWhite,
      appBar: AppBar(
        centerTitle: false,
        backgroundColor: appWhite,
        elevation: 0,
        title: AppTxt(
          text: homeState.dashboardTabs.isEmpty
              ? 'Loading...'
              : homeState.dashboardTabs[homeState.currentTabIndex].screenTitle,
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
        actions: [
          Visibility(
            visible: createJobState.entryExitReportLoading,
            child: const Center(
              child: CupertinoActivityIndicator(),
            ),
          ),
          Visibility(
            visible: jobsState.homeJobsLoading,
            child: const Padding(
              padding: EdgeInsets.all(10.0),
              child: CupertinoActivityIndicator(),
            ),
          ),
          Visibility(
            visible: jobsState.jobsLoading,
            child: const Padding(
              padding: EdgeInsets.all(10.0),
              child: CupertinoActivityIndicator(),
            ),
          ),
          Visibility(
            visible: homeState.userDetailsLoading,
            child: const Padding(
              padding: EdgeInsets.all(10.0),
              child: CupertinoActivityIndicator(),
            ),
          ),
          const SizedBox(width: 20),
        ],
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.white,
          systemNavigationBarColor: appBg,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.dark,
        ),
      ),
      bottomNavigationBar: Material(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        clipBehavior: Clip.hardEdge,
        child: BottomNavigationBar(
          backgroundColor: const Color(0XFF363636),
          currentIndex: homeState.currentTabIndex,
          unselectedItemColor: Colors.white,
          selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
          type: BottomNavigationBarType.fixed,
          onTap: (int index) async {
            hideSnackBar();
            Utils.hideKeyboard(context);
            homeState.currentTabIndex = index;
            homeState.profileEditMode = false;
            homeState.refresh();
            if (homeState.dashboardTabs[homeState.currentTabIndex]
                is AccountTab) {
              homeState.getUserInfo();
              LoginState loginState = context.read<LoginState>();
              loginState.getClients(fromHome: true);
            }
            if (homeState.dashboardTabs[homeState.currentTabIndex] is HomeTab) {
              getJobsForHomeTab();
            }
            if (homeState.dashboardTabs[homeState.currentTabIndex]
                is MyJobsTab) {
              JobsState jobsState = globalKey.currentContext!.read<JobsState>();
              jobsState.getJobs();
            }
          },
          items: (isDriver() || isSpotter() || isGuard()) &&
                  isTrailerAuditEnabled()
              ? const [
                  BottomNavigationBarItem(
                    icon: Icon(Icons.home_outlined),
                    label: 'Home',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.list_outlined),
                    label: 'My Spots',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.file_copy_outlined),
                    label: 'Audit',
                  ),
                  //commented--

                  // BottomNavigationBarItem(
                  //   icon: Icon(Icons.car_crash_outlined),
                  //   label: 'Damage',
                  // ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.person_outline),
                    label: 'Account',
                  ),
                ]
              : (isClient() && isTrailerAuditEnabled())
                  ? const [
                      BottomNavigationBarItem(
                        icon: Icon(Icons.home_outlined),
                        label: 'Home',
                      ),
                      // BottomNavigationBarItem(
                      //   icon: Icon(Icons.list_outlined),
                      //   label: 'My Spots',
                      // ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.file_copy_outlined),
                        label: 'Audit',
                      ),
                      //commented--

                      // BottomNavigationBarItem(
                      //   icon: Icon(Icons.car_crash_outlined),
                      //   label: 'Damage',
                      // ),
                      BottomNavigationBarItem(
                        icon: Icon(Icons.person_outline),
                        label: 'Account',
                      ),
                    ]
                  : (isClient())
                      ? const [
                          BottomNavigationBarItem(
                            icon: Icon(Icons.home_outlined),
                            label: 'Home',
                          ),
                          // BottomNavigationBarItem(
                          //   icon: Icon(Icons.list_outlined),
                          //   label: 'My Spots',
                          // ),
                          // BottomNavigationBarItem(
                          //   icon: Icon(Icons.file_copy_outlined),
                          //   label: 'Audit',
                          // ),
                          //commented--

                          // BottomNavigationBarItem(
                          //   icon: Icon(Icons.car_crash_outlined),
                          //   label: 'Damage',
                          // ),
                          BottomNavigationBarItem(
                            icon: Icon(Icons.person_outline),
                            label: 'Account',
                          ),
                        ]
                      : const [
                          BottomNavigationBarItem(
                            icon: Icon(Icons.home_outlined),
                            label: 'Home',
                          ),
                          BottomNavigationBarItem(
                            icon: Icon(Icons.list_outlined),
                            label: 'My Spots',
                          ),
                          //commented--

                          // BottomNavigationBarItem(
                          //   icon: Icon(Icons.car_crash_outlined),
                          //   label: 'Damage',
                          // ),
                          BottomNavigationBarItem(
                            icon: Icon(Icons.person_outline),
                            label: 'Account',
                          ),
                        ],
        ),
      ),
      floatingActionButton:
          homeState.dashboardTabs[homeState.currentTabIndex] == null ||
                  homeState.dashboardTabs[homeState.currentTabIndex]
                      is AccountTab ||
                  homeState.dashboardTabs[homeState.currentTabIndex]
                      is TrailerAuditScreen ||
                  homeState.dashboardTabs[homeState.currentTabIndex]
                      is DamageScreen
              ? const SizedBox()
              : FloatingActionButton(
                  heroTag: 'add_job',
                  child: const Icon(Icons.add),
                  onPressed: () async {
                    openCreateNewJobScreen();
                    CreateJobState createJobState =
                        context.read<CreateJobState>();
                    NewTrailerState newTrailerState =
                        context.read<NewTrailerState>();
                    createJobState.clearSelection();
                    createJobState.getSpotOnLocations();
                    createJobState.getTruckList();
                    createJobState.getDriversList('', '');
                    newTrailerState.resetState();
                  },
                ),
      body: homeState.dashboardTabs.isEmpty
          ? const SizedBox()
          : Container(
              color: Colors.white,
              padding: const EdgeInsets.all(20),
              child: homeState.dashboardTabs[homeState.currentTabIndex],
            ),
    );
  }
}
