import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:spot_on/utils/bol_dvir_status.dart';
import 'package:spot_on/utils/constants.dart';
import 'package:spot_on/utils/navigation_utils.dart';

import '../app_location.dart';
import '../providers/home_state.dart';
import '../utils/gaps.dart';
import '../utils/preference_utils.dart';

class LoginSuccessScreen extends StatefulWidget {
  const LoginSuccessScreen({Key? key}) : super(key: key);

  @override
  State<LoginSuccessScreen> createState() => _LoginSuccessScreenState();
}

class _LoginSuccessScreenState extends State<LoginSuccessScreen> {
  @override
  void initState() {
    navigate();
    super.initState();
  }

  Future<void> navigate() async {
    BolDvirStatus().reset().then((value) {
      HomeState homeState = globalKey.currentContext!.read<HomeState>();
      homeState.setDashboardTabs(Preferences.loginResponseModel!.roles.first);
    });
    await Future.delayed(const Duration(seconds: 2))
        .then((value) => openHome());
    AppLocation().requestLocationService();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        toolbarHeight: 0,
        elevation: 0,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: appBg,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: appBg,
        ),
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        width: double.infinity,
        color: appBg,
        child: Column(
          children: [
            const Spacer(),
            Image.asset(
              'assets/images/logo_new.png',
              width: MediaQuery.of(context).size.width * 0.8,
            ),
            gapH16,
            Preferences.isLoggedIn()
                ? const Text(
                    "Let's go to work!",
                    style: TextStyle(color: Colors.white),
                  )
                : const SizedBox.shrink(),
            const Spacer(),
          ],
        ),
      ),
    );
  }
}
