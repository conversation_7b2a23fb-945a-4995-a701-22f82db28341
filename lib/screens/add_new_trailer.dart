import 'package:spot_on/models/cliient_list_model.dart';
import 'package:spot_on/providers/new_trailer_state.dart';
import 'package:spot_on/utils/gaps.dart';
import 'package:spot_on/utils/imports.dart';

import '../models/supplieradd.dart';

// import '../models/addnew/carrieradd.dart';
// import '../models/addnew/supplieradd.dart';

class AddNewTrailer extends StatefulWidget {
  const AddNewTrailer({Key? key}) : super(key: key);

  @override
  State<AddNewTrailer> createState() => _AddNewTrailerState();
}

class _AddNewTrailerState extends State<AddNewTrailer> {
  bool selectCarrier = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      NewTrailerState newTrailerState = context.read<NewTrailerState>();

      newTrailerState.newTrailerReqModel.carrier = '';
      newTrailerState.loadCarriers();
    });
  }

  List<String> abc = ["FedEx", "YRC Freight", "DYNACRAFT"];

  @override
  Widget build(BuildContext context) {
    NewTrailerState newTrailerState = context.watch<NewTrailerState>();
    print('----------9--${newTrailerState.carrierList}');

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const AppTxt(
          text: 'Add New Trailer',
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: appGrey),
          onPressed: () async {
            Utils.hideKeyboard(globalKey.currentContext!);
            hideSnackBar();
            closeScreen();
          },
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(20),
        children: [
          AppTF(
            hintText: 'Unit Number *',
            onTextChanged: (val) async {
              newTrailerState.newTrailerReqModel.unitNumber = val;
            },
          ),
          gapH12,
          AppTF(
            hintText: 'Owner',
            initialValue: "A Blair",
            enabled: false,
            textStyle: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
            onTextChanged: (val) async {
              // newTrailerState.newTrailerReqModel.owner = val;
            },
          ),
          gapH24,
          const AppTxt(
            text: 'Select Client',
            fontWeight: FontWeight.bold,
          ),
          DropdownButton<SpotOnClient>(
            value: newTrailerState.selectedClient,
            icon: const Icon(Icons.arrow_drop_down_outlined),
            iconSize: 24,
            hint: const Padding(
              padding: EdgeInsets.only(
                left: 16,
                right: 16,
              ),
              child: AppTxt(text: 'Select Client'),
            ),
            elevation: 16,
            isExpanded: true,
            style: TextStyle(color: Theme.of(context).primaryColor),
            onChanged: (SpotOnClient? selectedClient) {
              if (selectedClient == null) {
                return;
              }
              newTrailerState.selectedClient = selectedClient;
              newTrailerState.refresh();
            },
            items: newTrailerState.clientListModel.list
                .map<DropdownMenuItem<SpotOnClient>>((SpotOnClient client) {
              return DropdownMenuItem<SpotOnClient>(
                value: client,
                child: AppTxt(
                  text: client.clientName ?? '',
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              );
            }).toList(),
          ),
          gapH6,
          const AppTxt(
            text: 'Select Carrier',
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),

          // Row(
          //   children: [
          //     Radio(
          //       value: true,
          //       groupValue: selectCarrier,
          //       onChanged: (val) {
          //         setState(() {
          //           newTrailerState.newTrailerReqModel.carrier = "";
          //           selectCarrier = val as bool;
          //         });
          //       },
          //       materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          //       visualDensity: const VisualDensity(
          //         horizontal: VisualDensity.minimumDensity,
          //         // vertical: VisualDensity.minimumDensity,
          //       ),
          //     ),
          //     gapW8,
          //     GestureDetector(
          //       onTap: () {
          //         setState(() {
          //           newTrailerState.newTrailerReqModel.carrier = "";
          //           selectCarrier = true;
          //         });
          //       },
          //       child: const AppTxt(
          //         text: 'Select Carrier',
          //         fontWeight: FontWeight.bold,
          //         fontSize: 12,
          //       ),
          //     ),
          //     const Spacer(),
          //     Radio(
          //       value: false,
          //       groupValue: selectCarrier,
          //       onChanged: (val) {
          //         setState(() {
          //           newTrailerState.newTrailerReqModel.carrier = "";
          //           selectCarrier = val as bool;
          //         });
          //       },
          //       materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          //       visualDensity: const VisualDensity(
          //         horizontal: VisualDensity.minimumDensity,
          //         // vertical: VisualDensity.minimumDensity,
          //       ),
          //     ),
          //     gapW8,
          //     GestureDetector(
          //       onTap: () {
          //         setState(() {
          //           newTrailerState.newTrailerReqModel.carrier = "";
          //           selectCarrier = false;
          //         });
          //       },
          //       child: const AppTxt(
          //         text: 'Add New Carrier',
          //         fontWeight: FontWeight.bold,
          //         fontSize: 12,
          //       ),
          //     ),
          //   ],
          // ),
          gapH4,
          DropdownButton<String>(
            value: newTrailerState.newTrailerReqModel.carrier.isEmpty
                ? null
                : newTrailerState.newTrailerReqModel.carrier,
            icon: const Icon(Icons.arrow_drop_down_outlined),
            iconSize: 24,
            hint: const AppTxt(text: 'Select Carrier'),
            elevation: 16,
            isExpanded: true,
            style: TextStyle(color: Theme.of(context).primaryColor),
            onChanged: (String? selectedCarrier) {
              if (selectedCarrier == null) return;

              // Update the selected carrier
              newTrailerState.newTrailerReqModel.carrier = selectedCarrier;
              newTrailerState.refresh();
            },
            items: newTrailerState.carrierList
                .where(
                    (carrier) => carrier.isNotEmpty) // Filter out empty strings
                .toSet() // Remove duplicates
                .toList() // Convert back to a List
                .map((String carrier) {
              return DropdownMenuItem<String>(
                value: carrier,
                child: AppTxt(
                  text: carrier,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              );
            }).toList(),
          ),

          // DropdownButton<String>(
          //   value: newTrailerState.newTrailerReqModel.carrier,
          //   icon: const Icon(Icons.arrow_drop_down_outlined),
          //   iconSize: 24,
          //   hint: const AppTxt(text: 'Select Carrier'),
          //   elevation: 16,
          //   isExpanded: true,
          //   style: TextStyle(color: Theme.of(context).primaryColor),
          //   onChanged: (String? selectedCarrier) {
          //     if (selectedCarrier == null) {
          //       return;
          //     }
          //     newTrailerState.newTrailerReqModel.carrier = selectedCarrier;
          //     newTrailerState.refresh();
          //   },
          //   items: newTrailerState.carrierList
          //       .map<DropdownMenuItem<String>>((String? carrier) {
          //     return DropdownMenuItem<String>(
          //       value: carrier,
          //       child: AppTxt(
          //         text: carrier.toString() ?? '',
          //         fontWeight: FontWeight.bold,
          //         fontSize: 16,
          //       ),
          //     );
          //   }).toList(),
          // ),
          // : AppTF(
          //     hintText: "Add New Carrier",
          //     initialValue: newTrailerState.newTrailerReqModel.carrier,
          //     onTextChanged: (val) {
          //       newTrailerState.newTrailerReqModel.carrier = val;
          //       newTrailerState.refresh();
          //     },
          //   ),
          // gapH8,

          AppTF(
            hintText: 'Remarks',
            onTextChanged: (val) async {
              newTrailerState.newTrailerReqModel.remarks = val;
            },
          ),
          gapH32,
          AppBtn(
            loading: newTrailerState.addingTruck,
            bgColor: Theme.of(context).primaryColor,
            color: Colors.white,
            text: 'Add',
            onPress: () async {
              print(
                  '---------0----${newTrailerState.newTrailerReqModel.unitNumber}');
              if (!newTrailerState.isValid()) {
                showSnackBar('Please enter Trailer Number', success: false);
                return;
              }
              if (null == newTrailerState.selectedClient) {
                showSnackBar('Please select a client', success: false);
                return;
              }
              newTrailerState.addTruck();
            },
          ),
        ],
      ),
    );
  }
}

class AddNewCarrier extends StatefulWidget {
  const AddNewCarrier({Key? key}) : super(key: key);

  @override
  State<AddNewCarrier> createState() => _AddNewCarrierState();
}

class _AddNewCarrierState extends State<AddNewCarrier> {
  TextEditingController carriertext = TextEditingController();
  String? selectedCarrier;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      // NewTrailerState newTrailerState = context.read<NewTrailerState>();
      // newTrailerState.newTrailerReqModel.carrier = "";
      // newTrailerState.loadCarriers();
    });
  }

  @override
  Widget build(BuildContext context) {
    NewTrailerState newTrailerState = context.watch<NewTrailerState>();
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const AppTxt(
          text: 'Add New Carrier',
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: appGrey),
          onPressed: () async {
            Utils.hideKeyboard(globalKey.currentContext!);
            hideSnackBar();
            closeScreen();
          },
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(20),
        children: [
          const AppTxt(
            text: 'Add New Carrier',
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
          gapH4,
          AppTF2(
            controller: carriertext,
            hintText: "",
            initialValue: '',
            onTextChanged: (val) {
              selectedCarrier = val;
            },
          ),
          gapH8,
          gapH32,
          AppBtn(
            loading: newTrailerState.addingTruck,
            bgColor: Theme.of(context).primaryColor,
            color: Colors.white,
            text: 'Add',
            onPress: () async {
              if (selectedCarrier == null || selectedCarrier!.isEmpty) {
                showSnackBar('Please add a carrier', success: false);
                return;
              }
              // CarrierSuccess response =
              //     await Services().addCarrierListEntryExit('$selectedCarrier');
              // if (response.carrierId?.length == 0) {
              //   showSnackBar('Carrier with provided name already exist in system.',
              //       success: false);
              // }
              else {
                // print('object-12----${response.carrierId}');
                carriertext.clear();
                showSnackBar('Success', success: true);
                Navigator.pop(context, selectedCarrier);
              }
              // Navigate back with the selected carrier as the result
              // Navigator.pop(context, selectedCarrier);
              // } else {
              //   showSnackBar('Failed to add carrier. Please try again.', success: false);
              // }
            },
          ),
        ],
      ),
    );
  }
}

class AddNewSupplier extends StatefulWidget {
  const AddNewSupplier({Key? key}) : super(key: key);

  @override
  State<AddNewSupplier> createState() => _AddNewSupplierState();
}

class _AddNewSupplierState extends State<AddNewSupplier> {
  TextEditingController suppliertext = TextEditingController();

  String? selectedsupplier;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      // NewTrailerState newTrailerState = context.read<NewTrailerState>();
      // newTrailerState.newTrailerReqModel.carrier = "";
      // newTrailerState.loadCarriers();
    });
  }

  @override
  Widget build(BuildContext context) {
    NewTrailerState newTrailerState = context.watch<NewTrailerState>();
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const AppTxt(
          text: 'Add New Supplier',
          fontWeight: FontWeight.bold,
          fontSize: 16,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: appGrey),
          onPressed: () async {
            Utils.hideKeyboard(globalKey.currentContext!);
            hideSnackBar();
            closeScreen();
          },
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(20),
        children: [
          const AppTxt(
            text: 'Add New Supplier',
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
          gapH4,
          AppTF2(
            controller: suppliertext,
            hintText: "",
            initialValue: '',
            onTextChanged: (val) {
              selectedsupplier = val;
            },
          ),
          gapH32,
          AppBtn(
            loading: newTrailerState.addingTruck,
            bgColor: Theme.of(context).primaryColor,
            color: Colors.white,
            text: 'Add',
            onPress: () async {
              if (selectedsupplier == null || selectedsupplier!.isEmpty) {
                showSnackBar('Please add a supplier', success: false);
                return;
              }
              SupplierSuccess response = await Services()
                  .addSupplierListEntryExit('$selectedsupplier');
              if (response.supplierId?.length == 0) {
                showSnackBar(
                    'Supplier with provided name already exist in system.',
                    success: false);
              } else {
                print('object-12----${response.supplierId.toString()}');
                suppliertext.clear();
                showSnackBar('Success', success: true);
                Navigator.pop(context, response);
              }
              // Navigate back with the selected carrier as the result
              // Navigator.pop(context, selectedCarrier);
              // } else {
              //   showSnackBar('Failed to add carrier. Please try again.', success: false);
              // }
            },
          ),
        ],
      ),
    );
  }
}
