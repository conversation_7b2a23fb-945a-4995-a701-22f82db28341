import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:spot_on/utils/constants.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewScreen extends StatefulWidget {
  const WebViewScreen({
    required this.url,
    required this.title,
    Key? key,
  }) : super(key: key);

  final String url;
  final String title;

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  bool _loading = true;

  @override
  void initState() {
    super.initState();

    controller = WebViewController()
      ..setNavigationDelegate(NavigationDelegate(
        onPageStarted: (url) {
          setState(() {
            _loading = true;
          });
        },
        onProgress: (progress) {
          setState(() {});
        },
        onPageFinished: (url) {
          setState(() {
            _loading = false;
          });
        },
      ))
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(
        Uri.parse(widget.url),
      );
  }

  late final WebViewController controller;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: Text(widget.title),
        systemOverlayStyle:
            const SystemUiOverlayStyle(statusBarColor: appColor),
      ),
      body: ModalProgressHUD(
        opacity: 0,
        color: Colors.white,
        inAsyncCall: _loading,
        child: WebViewWidget(
          controller: controller,
        ),
      ),
    );
  }
}
