import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:intl/intl.dart';
import 'package:spot_on/models/client_listentryexit.dart';
import 'package:spot_on/models/driverlistentryexit.dart';
import 'package:spot_on/models/location_listentryexit.dart';
import 'package:spot_on/models/spot_list_entryexit.dart';
import 'package:spot_on/models/supplieradd.dart';
import 'package:spot_on/models/supplierlist.dart';
import 'package:spot_on/models/truck_list_model.dart';
import 'package:spot_on/providers/create_job_state.dart';
import 'package:spot_on/providers/jobs_state.dart';
import 'package:spot_on/utils/imports.dart';
import 'package:spot_on/widgets/add_trailer.dart';
import 'package:spot_on/widgets/app_ta.dart';

import '../cubits/entry_exit/cubit/entry_exit_cubit.dart';
import '../models/cliient_list_model.dart';

// class EntryExitTab extends StatelessWidget {
//   const EntryExitTab({Key? key}) : super(key: key);
//   final String screenTitle = 'Entry-Exit';

//   @override
//   Widget build(BuildContext context) {
//     CreateJobState createJobState = context.watch<CreateJobState>();
//     return GlobalWidget(
//       child: RefreshIndicator(
//         onRefresh: () async {
//           JobsState jobsState = globalKey.currentContext!.read<JobsState>();
//           CreateJobState cJobState = globalKey.currentContext!.read<CreateJobState>();
//           await jobsState.getHomeJobs();
//           cJobState.getEntryExitReportList();
//         },
//         child: ListView(
//           physics: const ClampingScrollPhysics(),
//           children: [
//             _entryExitButtons(context, createJobState),
//             const SizedBox(height: 40),
//             const AppTxt(
//               text: 'Location',
//               fontSize: 12,
//               fontWeight: FontWeight.w500,
//             ),
//             DropdownButton<SpotOnLocation>(
//               hint: const AppTxt(text: 'Select Location'),
//               value: createJobState.selectedEntryExitLocation,
//               icon: createJobState.spotsLoading ? const CupertinoActivityIndicator() : const Icon(Icons.arrow_drop_down_outlined),
//               iconSize: 24,
//               elevation: 16,
//               isExpanded: true,
//               style: TextStyle(color: Theme.of(context).primaryColor),
//               onChanged: (SpotOnLocation? spotOnLocation) {
//                 if (createJobState.spotOnLocationsLoading) {
//                   return;
//                 }
//                 createJobState.selectedEntryExitLocation = spotOnLocation;
//                 createJobState.refresh();
//               },
//               items: createJobState.spotOnLocationList.list.map<DropdownMenuItem<SpotOnLocation>>((SpotOnLocation value) {
//                 return DropdownMenuItem<SpotOnLocation>(
//                   value: value,
//                   child: Text(
//                     value.locationName ?? '',
//                     style: const TextStyle(color: appBg),
//                   ),
//                 );
//               }).toList(),
//             ),
//             const SizedBox(height: 20),
//             const AppTxt(
//               text: 'Trailer',
//               fontSize: 12,
//               fontWeight: FontWeight.w500,
//             ),
//             TypeAheadField(
//               textFieldConfiguration: TextFieldConfiguration(
//                 autofocus: false,
//                 decoration: const InputDecoration(
//                   hintText: "Search Trailer",
//                 ),
//                 controller: createJobState.autoCompleteController,
//               ),
//               getImmediateSuggestions: false,
//               hideOnEmpty: true,
//               minCharsForSuggestions: 1,
//               suggestionsCallback: (pattern) async {
//                 var result = await Services.getTruckListEntryExit(pattern);
//                 if (result is Success) {
//                   var truckListModel = result.response as TruckListModel;
//                   return truckListModel.list.where(
//                     (e) => e.unitNumber!.toLowerCase().contains(
//                           pattern.toLowerCase(),
//                         ),
//                   );
//                 }
//                 return [];
//               },
//               loadingBuilder: (context) {
//                 return const Center(
//                   child: SizedBox(
//                     height: 16,
//                     width: 16,
//                     child: CircularProgressIndicator(strokeWidth: 1),
//                   ),
//                 );
//               },
//               itemBuilder: (context, suggestion) {
//                 TruckDetail? truckDetail = suggestion as TruckDetail;
//                 return Padding(
//                   padding: const EdgeInsets.all(10.0),
//                   child: AppTxt(
//                     text: truckDetail.unitNumber?.replaceAll('-', ' ') ?? '',
//                     color: truckDetail.isHotTrailer ? Colors.red : Colors.black,
//                     fontWeight: FontWeight.bold,
//                   ),
//                 );
//               },
//               onSuggestionSelected: (suggestion) {
//                 TruckDetail? truckDetail = suggestion as TruckDetail;
//                 if (truckDetail == null) {
//                   return;
//                 }
//                 createJobState.selectedTruckDetail = truckDetail;
//                 createJobState.jobRequestModel.fleetId = truckDetail.fleetId!;
//                 createJobState.autoCompleteController.text = truckDetail.unitNumber ?? '';
//                 createJobState.refresh();
//               },
//             ),
//             const SizedBox(height: 20),
//             const AddTrailer(),
//             const SizedBox(height: 10),
//             AppTA(
//               key: UniqueKey(),
//               hintText: 'Notes',
//               labelText: 'Notes',
//               minLines: 1,
//               onTextChange: (val) async {
//                 createJobState.entryExitNotes = val;
//               },
//             ),
//             const SizedBox(height: 20),
//             AppBtn(
//               loading: createJobState.entryExitLoading,
//               loadingColor: Colors.white,
//               bgColor: Theme.of(context).primaryColor,
//               color: Colors.white,
//               fontWeight: FontWeight.bold,
//               text: createJobState.exitSelected ? 'Exit Now' : 'Enter Now',
//               onPress: () async {
//                 if (null == createJobState.selectedEntryExitLocation) {
//                   showSnackBar('Please Select Location', success: false);
//                   return;
//                 }
//                 if (null == createJobState.selectedTruckDetail) {
//                   if (createJobState.autoCompleteController.text.isNotEmpty) {
//                     showSnackBar(
//                       'Invalid Unit/Trailer #. Please verify and enter the correct trailer/unit #',
//                       success: false,
//                     );
//                     return;
//                   } else {
//                     showSnackBar('Please Select Trailer', success: false);
//                     return;
//                   }
//                 }
//                 if (createJobState.entryExitNotes.trim().isEmpty) {
//                   showSnackBar('Please enter notes', success: false);
//                   return;
//                 }
//                 hideSnackBar();
//                 createJobState.doFleetEntryExit();
//               },
//             ),
//             const SizedBox(height: 40),
//             const AppTxt(
//               text: 'Entry/Exit Report',
//               fontSize: 18,
//               fontWeight: FontWeight.bold,
//             ),
//             const SizedBox(height: 20),
//             ListView.builder(
//               shrinkWrap: true,
//               physics: const NeverScrollableScrollPhysics(),
//               itemCount: createJobState.entryExitReportListModel.list.length,
//               itemBuilder: (context, index) {
//                 EntryExit entryExit = createJobState.entryExitReportListModel.list[index];
//                 Color color = entryExit.type?.toLowerCase() == 'entry'
//                     ? Theme.of(context).primaryColorDark.withOpacity(0.6)
//                     : Theme.of(context).primaryColorDark.withOpacity(0.4);
//                 return Container(
//                   padding: const EdgeInsets.all(10),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Container(
//                         padding: const EdgeInsets.fromLTRB(12, 8, 12, 5),
//                         decoration: BoxDecoration(
//                           color: color,
//                           border: Border.all(color: color),
//                           borderRadius: BorderRadius.circular(20),
//                         ),
//                         child: AppTxt(
//                           text: entryExit.type ?? '',
//                           fontSize: 12,
//                           lineHeight: 1,
//                           color: appWhite,
//                         ),
//                       ),
//                       const SizedBox(height: 5),
//                       AppTxt(text: entryExit.fleet.remarks ?? ''),
//                       const SizedBox(height: 5),
//                       AppTxt(
//                         text: entryExit.location?.locationName ?? '',
//                         fontWeight: FontWeight.bold,
//                       ),
//                       const SizedBox(height: 5),
//                       AppTxt(
//                         text: '${entryExit.location?.street}, ${entryExit.location?.city}, ${entryExit.location?.state}, ${entryExit.location?.country}',
//                       ),
//                       const SizedBox(height: 5),
//                       AppTxt(text: entryExit.notes ?? ''),
//                       const SizedBox(height: 5),
//                       AppTxt(
//                         text: 'Created: ${entryExit.audit!.createdDate}',
//                         fontSize: 14,
//                         color: appGrey,
//                       ),
//                       const SizedBox(height: 3),
//                       AppTxt(
//                         text: 'Last Modified: ${entryExit.audit!.lastModifiedDate}',
//                         fontSize: 14,
//                         color: appGrey,
//                       ),
//                       const SizedBox(height: 3),
//                       AppTxt(
//                         text: 'Created By: ${entryExit.audit!.createdBy?.firstName}  ${entryExit.audit!.createdBy?.lastName}',
//                         fontSize: 14,
//                         color: appGrey,
//                       ),
//                       const SizedBox(height: 5),
//                       Divider(color: appGrey),
//                     ],
//                   ),
//                 );
//               },
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   _entryExitButtons(BuildContext context, CreateJobState jobsState) {
//     return Container(
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: const BorderRadius.all(
//           Radius.circular(40.0),
//         ),
//         boxShadow: [
//           BoxShadow(
//             color: Colors.grey.withOpacity(0.3),
//             spreadRadius: 3,
//             blurRadius: 9,

//             // offset: Offset(3, 3),
//           ),
//         ],
//       ),
//       child: Row(
//         children: [
//           Expanded(
//             child: AppBtn(
//               text: 'Entry',
//               borderColor: jobsState.exitSelected ? Colors.transparent : Theme.of(context).primaryColor,
//               bgColor: jobsState.exitSelected ? Colors.transparent : Theme.of(context).primaryColor,
//               color: jobsState.exitSelected ? Theme.of(context).primaryColor : Colors.white,
//               fontWeight: FontWeight.bold,
//               onPress: () async {
//                 jobsState.setExitSelected(false);
//                 hideSnackBar();
//               },
//             ),
//           ),
//           Expanded(
//             child: AppBtn(
//               text: 'Exit',
//               borderColor: !jobsState.exitSelected ? Colors.transparent : Theme.of(context).primaryColor,
//               bgColor: !jobsState.exitSelected ? Colors.transparent : Theme.of(context).primaryColor,
//               color: !jobsState.exitSelected ? Theme.of(context).primaryColor : Colors.white,
//               fontWeight: FontWeight.bold,
//               onPress: () async {
//                 jobsState.setExitSelected(true);
//               },
//             ),
//           )
//         ],
//       ),
//     );
//   }
// }

class EntryExitTab extends StatefulWidget {
  const EntryExitTab({Key? key}) : super(key: key);
  final String screenTitle = 'Create Entry/ Exit Detail';

  @override
  State<EntryExitTab> createState() => _EntryExitTabState();
}

class _EntryExitTabState extends State<EntryExitTab> {
  final TextEditingController _controllerpickup = TextEditingController();
  final TextEditingController _controllerarrival = TextEditingController();

  final TextEditingController _controllercarrier = TextEditingController();
  final TextEditingController _controllerlocation = TextEditingController();
  final TextEditingController _controllerspot = TextEditingController();
  final TextEditingController _controllerclient = TextEditingController();
  final TextEditingController _controlleruser = TextEditingController();
  final TextEditingController _controllersupplier = TextEditingController();
  final TextEditingController _controllertrailer = TextEditingController();
  final TextEditingController _controllerdueplant = TextEditingController();
  final TextEditingController _controllersequence = TextEditingController();
  final TextEditingController _controllerloadstatus = TextEditingController();
  final TextEditingController _controllersub = TextEditingController();
  final TextEditingController _controllerpronumber = TextEditingController();
  final TextEditingController _controllertractor = TextEditingController();
  final TextEditingController _controllerbill = TextEditingController();
  final TextEditingController updatedby = TextEditingController();
  final TextEditingController _controllernotes = TextEditingController();

  String? carrierName;
  String? clientId;
  String? supplierName;
  String? selectedOption;
  String? bill;
  String? trailerNuumber;
  String? sub;
  String? proNumber;
  String? dueplant;
  String? datepickup;
  String? loadstatus;
  String? tractornumber;
  String? arrivaldate;
  String? sequence;
  String? spot;
  String? location;
  String? driverId;
  String? img;
  String? fleetId;
  ClientListElement? selectedClient;
  UsersListElement? selecteddriver; // For client dropdown
  ListLocationElement? selectedLocation; // For location dropdown
  String? selectedOptiondropdown; // For type dropdown
  SpotListElement? selectedSpot; // For spot dropdown
  String? selectedCarrier; // For carrier dropdown
  ListSupplier? selectedSupplier;
  List<String>? carrierdetails;
  SupplierList? supplierdetails;
  SpotListeEntryExit? spotdetails;
  LocationListeEntryExit? locdetails;
  UsersListeEntryExit? driverdetails;
  ClientListeEntryExit? clientdetails;
  TruckDetail? selectedTruck;
  String? selectedValueLoadStatus;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    updatedby.text =
        '${Preferences.loginResponseModel?.firstName} ${Preferences.loginResponseModel?.lastName}';
    context.read<EntryExitCubit>().getClientEntryExit();

    context.read<EntryExitCubit>().getCarrierEntry();
    context.read<EntryExitCubit>().getSupplierEntry();
    // context.read<EntryExitCubit>().getSpotEntryExit('');
    context.read<EntryExitCubit>().getLocationEntryExit();
    context.read<EntryExitCubit>().getDriverEntryExit();
  }

  getclient() async {
    SpotOnClient? spotOnClient = await Preferences.getSelectedClient();
    _controllerclient.text =
        spotOnClient?.clientName ?? ''; // Update controller text
    clientId = spotOnClient?.clientId;
    selectedClient = getClientValue(spotOnClient);
    print('dsfff${_controllerclient.text}');
  }

  @override
  Widget build(BuildContext context) {
    print('carrrrrr---$carrierdetails');
    print('usr---------${Preferences.loginResponseModel!.userId}');

    // CreateJobState createJobState = context.watch<CreateJobState>();
    return BlocConsumer<EntryExitCubit, EntryExitState>(
        listener: (context, state) async {
      if (state is SuccessSupplier) {
        print('object-----${state.supplierdetails}');
        setState(() {
          supplierdetails = state.supplierdetails;
        });
      }
      if (state is SuccessCarrier) {
        setState(() {
          carrierdetails = state.carrierdetails;
        });
      }
      if (state is SuccessLocation) {
        setState(() {
          locdetails = state.locdetails;
        });
      }

      if (state is SuccessSpot) {
        setState(() {
          spotdetails = state.spotdetails;
        });
      }
      if (state is SuccessClient) {
        print('object----save');
        setState(() {
          clientdetails = state.clientdetails;
          getclient();
        });
      }
      if (state is SuccessCreateTrailer) {
        List<TruckDetail> truck;
        setState(() {
          truck = state.list;
          selectedTruck = truck.first;

          selectedCarrier = state.list.first.carrier.toString();
          _controllercarrier.text = state.list.first.carrier.toString() ?? '';
          // selectedCarrier=truck.first.carrier as ListElement?;-----e
          _controllertrailer.text = truck.first.unitNumber.toString();
        });
        print('risaj---id----${_controllertrailer.text}');
      }
      if (state is FailedMessageSubmit) {
        showSnackBar('Something went wrong', success: false);
      }
      if (state is FailedSubmit) {
        showSnackBar(state.msgs, success: false);
      }
      if (state is SuccessDriver) {
        setState(() {
          driverdetails = state.driverdetails;
        });
      }
      if (state is SuccessSubmit) {
        showSnackBar('Success');
        clearFields();
      }
      if (state is SuccessAutofill) {
        clearLocationFields();
        var a = state.list;
        print('test123------ZZZZ');
        setState(() {
          if (a.spot?.locationName != null) {
            print('test123------ZZZZlocation');

            context
                .read<EntryExitCubit>()
                .getSpotEntryExit('${a.spot?.locationId.toString()}');
            _controllerlocation.text = a.spot?.locationName.toString() ?? '';
            location = a.spot?.locationId.toString() ?? '';
            selectedLocation = ListLocationElement(
                clientId: "abc123",
                locationId: a.spot?.locationId.toString() ?? '',
                locationName: a.spot?.locationName.toString() ?? '',
                street: "123 Main St",
                city: "Springfield",
                state: "IL",
                zip: "62701",
                country: "USA",
                latitude: 39.7817,
                longitude: -89.6501,
                locationMapJson:
                    "{\"zoom\": 15, \"center\": [39.7817, -89.6501]}",
                remarks: "Main storage area",
                isActive: true,
                isDefault: false,
                mapImageUrl: "https://example.com/map-image.jpg",
                pieChartColor: "#FF5733",
                audit: null);
          }

          if (a.spot?.spotId != null) {
            print('test123------ZZZZspot');

            _controllerspot.text = a.spot?.spotName ?? ''; // Update text field
            spot = a.spot?.spotId;
            selectedSpot = SpotListElement(
              locationId: a.spot?.locationId.toString() ?? '',
              locationName: a.spot?.locationName.toString() ?? '',
              latitude: 39.7817,
              longitude: -89.6501,
              remarks: "Main storage area",
              isActive: true,
              spotId: a.spot?.spotId.toString() ?? '',
              spotName: a.spot?.spotName.toString() ?? '',
            );
          } //
        });

        // showSnackBar('Success');
        // clearFields();
      }
      // autofillSpot
    }, builder: (context, state) {
      print('calendar------${_controllerpickup.text}');

      print('state is-----$state');
      if (state is EntryExitInitial) {
        return RefreshIndicator(
            onRefresh: () async {
              context.read<EntryExitCubit>().getCarrierEntry();
              context.read<EntryExitCubit>().getSupplierEntry();
              // context.read<EntryExitCubit>().getSpotEntryExit('');
              context.read<EntryExitCubit>().getLocationEntryExit();
              context.read<EntryExitCubit>().getDriverEntryExit();
              context.read<EntryExitCubit>().getClientEntryExit();
            },
            child: const Center(
              child: CircularProgressIndicator(),
            ));
      } else {
        // (state is EntryExitInitial) ? context.loaderOverlay.show() : context.loaderOverlay.hide();
        return RefreshIndicator(
          onRefresh: () async {
            JobsState jobsState = globalKey.currentContext!.read<JobsState>();
            CreateJobState cJobState =
                globalKey.currentContext!.read<CreateJobState>();
            await jobsState.getHomeJobs();
            await cJobState.getEntryExitReportList();
            print('value-----');
            // JobsState jobsState = globalKey.currentContext!.read<JobsState>();
            // CreateJobState cJobState =
            //     globalKey.currentContext!.read<CreateJobState>();
            // await jobsState.getHomeJobs();
            // cJobState.getEntryExitReportList();
          },
          child: ListView(
            physics: const ClampingScrollPhysics(),
            children: [
              // ElevatedButton(
              //     onPressed: () {
              //       Services().sendFormData();
              //     },
              //     child: Text('vk')),
              // _entryExitButtons(context, createJobState),
              // const SizedBox(height: 40),
              const Row(
                children: [
                  AppTxt(
                    text: 'Type',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              DropdownButton<String>(
                items: <String>['ENTRY', 'EXIT'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                value: selectedOption,
                hint: const Text(
                  "Select Type",
                  style: TextStyle(fontSize: 12),
                ),
                onChanged: (String? newValue) {
                  setState(() {
                    selectedOption = newValue;
                  });
                  if (newValue == 'EXIT' && selectedTruck != null) {
                    context
                        .read<EntryExitCubit>()
                        .autofillSpot('${selectedTruck?.fleetId}');
                  }
                },
                isExpanded: true, // To make the dropdown full-width
              ),
              const SizedBox(height: 20),
              const Row(
                children: [
                  AppTxt(
                    text: 'Select Client',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                    horizontal: 4.0), // Padding around the dropdown
                decoration: const BoxDecoration(
                    // border: Border.all(color: Colors.grey), // Optional border styling
                    // borderRadius: BorderRadius.circular(5.0),
                    ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: DropdownButton<ClientListElement>(
                        hint: const Text(
                          "Select Client",
                          style: TextStyle(fontSize: 14),
                        ),
                        value: selectedClient, // Current selected client
                        items: clientdetails?.list
                                ?.map<DropdownMenuItem<ClientListElement>>(
                                    (client) {
                              return DropdownMenuItem<ClientListElement>(
                                value: client,
                                child: Text(client.clientName!),
                              );
                            }).toList() ??
                            [], // Provide an empty list if clientdetails is null
                        onChanged: (ClientListElement? newValue) {
                          setState(() {
                            selectedClient = newValue; // Update selected client
                            _controllerclient.text = newValue?.clientName ??
                                ''; // Update controller text
                            clientId = newValue?.clientId; // Update client ID
                          });
                          print('Selected: ${newValue?.clientName}');
                        },
                        isExpanded: true, // Make dropdown take full width
                        // underline: SizedBox(), // Remove default underline
                      ),
                    ),
                    // const Icon(Icons.arrow_drop_down), // Rightmost dropdown icon
                  ],
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              const Row(
                children: [
                  AppTxt(
                    text: 'Trailer#',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              TypeAheadField(
                textFieldConfiguration: TextFieldConfiguration(
                  autofocus: false,
                  decoration: const InputDecoration(
                    suffixIcon: Icon(Icons.arrow_drop_down),
                    hintText: "Search Trailer",
                  ),
                  controller: _controllertrailer,
                ),
                getImmediateSuggestions: false,
                hideOnEmpty: true,
                minCharsForSuggestions: 1,
                suggestionsCallback: (pattern) async {
                  var result = await Services.getTruckListNew(pattern);
                  if (result is Success) {
                    print('risaj-----');
                    var truckListModel = result.response as TruckListModel;
                    print('risaj----oo-');

                    return truckListModel.list.where(
                      (e) => e.unitNumber!.toLowerCase().contains(
                            pattern.toLowerCase(),
                          ),
                    );
                  }
                  return [];
                },
                loadingBuilder: (context) {
                  return const Center(
                    child: SizedBox(
                      height: 16,
                      width: 16,
                      child: CircularProgressIndicator(strokeWidth: 1),
                    ),
                  );
                },
                itemBuilder: (context, suggestion) {
                  TruckDetail? truckDetail = suggestion as TruckDetail;
                  return Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: AppTxt(
                      text: truckDetail.unitNumber?.replaceAll('-', ' ') ?? '',
                      color: truckDetail.isHotTrailer ?? false
                          ? Colors.red
                          : Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                },
                onSuggestionSelected: (suggestion) {
                  TruckDetail? truckDetail = suggestion as TruckDetail;

                  setState(() {
                    selectedTruck = suggestion;
                    _controllertrailer.text = '${suggestion.unitNumber}';
                    fleetId = suggestion.fleetId;
                  });
                  if (selectedOption == 'EXIT') {
                    print('EXIT----');

                    context
                        .read<EntryExitCubit>()
                        .autofillSpot('${suggestion.fleetId}');
                  }

                  // trailerAuditState.selectedTruckDetail = truckDetail;
                  // trailerAuditState.carrier = truckDetail.carrier;
                  // trailerAuditState.autoCompleteController.text = truckDetail.unitNumber ?? '';
                  // trailerAuditState.refresh();
                },
              ),
              // AppTA1(
              //   key: UniqueKey(),
              //   hintText: 'Trailer Number',
              //   labelText: 'Type Trailer Number',
              //   minLines: 1,
              //   onTextChange: (val) async {
              //     trailerNuumber = val;
              //     //commented
              //   },
              // ),
              const SizedBox(height: 10),

              const AddTrailer1(),

              const SizedBox(height: 20),

              const Row(
                children: [
                  AppTxt(
                    text: 'Location',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                    horizontal: 4.0), // Padding around the dropdown
                decoration: const BoxDecoration(
                    // border: Border.all(color: Colors.grey), // Optional border styling
                    // borderRadius: BorderRadius.circular(5.0),
                    ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: DropdownButton<ListLocationElement>(
                        hint: const Text(
                          "Select Location",
                          style: TextStyle(fontSize: 14),
                        ),

                        items: locdetails?.list
                            ?.map(
                                (loc) => DropdownMenuItem<ListLocationElement>(
                                      value: loc,
                                      child: Text(loc.locationName!),
                                    ))
                            .toList()
                            .cast<DropdownMenuItem<ListLocationElement>>(),
                        onChanged: (ListLocationElement? selectedLocation) {
                          setState(() {
                            _controllerlocation.text =
                                selectedLocation?.locationName ?? '';
                            location = selectedLocation?.locationId;
                            this.selectedLocation = selectedLocation;
                          });
                          spotdetails = null;
                          selectedSpot = null;
                          spot = null;
                          context.read<EntryExitCubit>().getSpotEntryExit(
                              '${selectedLocation?.locationId}');

                          print('Selected: ${selectedLocation?.locationName}');
                        },
                        value: getLocationValue(selectedLocation),
                        isExpanded: true, // Expands dropdown to take full width
                        // underline: SizedBox(), // Removes default underline
                      ),
                    ),
                    // const Icon(Icons.arrow_drop_down), // Dropdown arrow on the right
                  ],
                ),
              ),

              const SizedBox(height: 20),

              const Row(
                children: [
                  AppTxt(
                    text: 'Spot',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                    horizontal: 4.0), // Optional padding
                decoration: const BoxDecoration(
                    // border: Border.all(color: Colors.grey), // Border styling
                    // borderRadius: BorderRadius.circular(5.0), // Rounded corners
                    ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: DropdownButton<SpotListElement>(
                        hint: const Text(
                          "Select Spot",
                          style: TextStyle(fontSize: 14),
                        ),
                        value: getSpotValue(
                            selectedSpot), // Variable holding the currently selected spot
                        items: (spotdetails?.list ?? [])
                            .map<DropdownMenuItem<SpotListElement>>((spot) {
                          return DropdownMenuItem<SpotListElement>(
                            value: spot,
                            child: Text(spot.spotName!),
                          );
                        }).toList(),
                        onChanged: (SpotListElement? newSpot) {
                          setState(() {
                            selectedSpot = newSpot; // Update selected spot
                            _controllerspot.text =
                                newSpot?.spotName ?? ''; // Update text field
                            spot = newSpot?.spotId; // Update spot ID
                          });
                          print('Selected: ${newSpot?.spotName}');
                        },
                        isExpanded: true, // Expands dropdown to full width
                        // underline: const SizedBox(), // Removes default underline
                      ),
                    ),
                    // const Icon(Icons.arrow_drop_down), // Right-aligned dropdown arrow icon
                  ],
                ),
              ),

              const SizedBox(height: 20),
              // if (selectedSpotId != null)
              //   Text("Selected Spot ID: $selectedSpotId"),
              //     ],
              //   ),
              // ),
              const SizedBox(height: 20),
              const AppTxt(
                text: 'Pro Number',
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              AppTA1(
                controller: _controllerpronumber,
                // key: UniqueKey(),
                hintText: 'Pro Number',
                labelText: 'Type Pro Number',
                minLines: 1,
                onTextChange: (val) async {
                  proNumber = val;
                  //commented
                },
              ),
              const SizedBox(height: 20),

              const Row(
                children: [
                  AppTxt(
                    text: 'Driver',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),

              TypeAheadField<UsersListElement>(
                textFieldConfiguration: TextFieldConfiguration(
                  autofocus: false,
                  decoration: const InputDecoration(
                    suffixIcon: Icon(Icons.arrow_drop_down),
                    hintText: "Search Driver",
                    // You can customize the hint text to "Search Driver"
                  ),
                  controller:
                      _controlleruser, // Use the controller for managing input text
                ),
                getImmediateSuggestions:
                    false, // Delay suggestions until user starts typing
                hideOnEmpty: true, // Hide suggestions if the input is empty
                minCharsForSuggestions:
                    1, // Start suggesting after typing 1 character
                suggestionsCallback: (pattern) async {
                  driverdetails =
                      await Services().getDriverListEntryExit(pattern);
                  // Fetch the list of drivers based on the pattern
                  var result = driverdetails?.list?.where((e) {
                    // Filter users based on the roles containing "driver"
                    return e.roles != null &&
                        e.roles!.isNotEmpty &&
                        e.roles!.any((role) =>
                            role.roleName!.toLowerCase().contains("driver"));
                  }).toList();

                  return result?.where((e) => '${e.firstName} ${e.lastName}'
                          .toLowerCase()
                          .contains(pattern.toLowerCase())) ??
                      [];
                },
                loadingBuilder: (context) {
                  // Show loading indicator while fetching suggestions
                  return const Center(
                    child: SizedBox(
                      height: 16,
                      width: 16,
                      child: CircularProgressIndicator(strokeWidth: 1),
                    ),
                  );
                },
                itemBuilder: (context, suggestion) {
                  // Render each suggestion in the list
                  UsersListElement user = suggestion;
                  return Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Text(
                      '${user.firstName!} ${user.lastName!}', // Display driver name
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  );
                },
                onSuggestionSelected: (suggestion) {
                  // Handle selection of a driver
                  UsersListElement user = suggestion;

                  setState(() {
                    selecteddriver = user;
                    _controlleruser.text =
                        '${user.firstName!} ${user.lastName!}'; // Update the text field with selected driver name
                    driverId = user.userId; // Store driver ID if needed
                  });
                  print('Selected: ${user.firstName} ${user.lastName}');
                },
              ),

              const SizedBox(
                height: 20,
              ),

              const AppTxt(
                text: 'Bill of Lading',
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),

              AppTAImage(
                controller: _controllerbill, imagePath: img,
                // key: UniqueKey(),
                hintText: 'Type Bill Lading',
                labelText: 'Type Pro Number',
                minLines: 1,
                onTextChange: (text) async {
                  setState(() {
                    _controllerbill.text = text;
                  });
                  //commented
                },
                onImageChange: (imagePath) {
                  setState(() {
                    img = imagePath;
                  });
                },
              ),
              // AppTA1(
              //   key: UniqueKey(),
              //   hintText: 'Carrier Name',
              //   labelText: 'Type Pro Number',
              //   minLines: 1,
              //   onTextChange: (val) async {
              //     createJobState.entryExitNotes = val;
              //   },
              // ),
              const SizedBox(height: 20),
              const Row(
                children: [
                  AppTxt(
                    text: 'Carrier',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),

              TypeAheadField<String>(
                textFieldConfiguration: TextFieldConfiguration(
                  autofocus: false,
                  controller: _controllercarrier,
                  decoration: const InputDecoration(
                    suffixIcon: Icon(Icons.arrow_drop_down),
                    hintText: "Select Carrier",
                    // border: OutlineInputBorder(),
                  ),
                ),
                getImmediateSuggestions: false,
                hideOnEmpty: false,
                minCharsForSuggestions: 1,
                suggestionsCallback: (pattern) async {
                  carrierdetails =
                      await Services().getCarrierSearchListEntryExit(pattern);
                  // return carrierdetails!.list!.where((carrier) {
                  //   return carrier.carrier!.toLowerCase().contains(pattern.toLowerCase());
                  // }).toList();
                  return carrierdetails ?? [];
                  // .where((carrier) {
                  //       // Check if carrier and carrier.carrier are not null before accessing
                  //       return carrier.carrier != null &&
                  //           carrier.carrier!
                  //               .toLowerCase()
                  //               .contains(pattern.toLowerCase());
                  //     }).toList() ??
                  //     [];
                },
                itemBuilder: (context, String suggestion) {
                  return ListTile(
                    title: Text(suggestion),
                  );
                },
                onSuggestionSelected: (String? suggestion) {
                  setState(() {
                    selectedCarrier = suggestion;
                    _controllercarrier.text = suggestion.toString();
                  });
                  print('Selected: $suggestion');
                },
              ),

              const SizedBox(height: 10),

              AddCarrier(
                onCarrierAdded: (val) async {
                  print('object--------$val');
                  // context.read<EntryExitCubit>().getCarrierEntry();
                  // CarrierSuccess b = val;
                  // print('value------${b.carrierId}');
                  if (val != null) {
                    setState(() {
                      selectedCarrier = val;
                      //  ListElement(
                      //     id: b.id,
                      //     carrier: '${b.carrier}',
                      //     carrierId: '${b.carrierId}');
                      _controllercarrier.text = val.toString();
                    });
                  }
                  showSnackBar('Success');
                },
              ),

//            AddCarrier(onCarrierAdded: (val){
//             CarrierSuccess b=val;
// showSnackBar('${b.carrierId}----${b.carrier}---');

//           },),

              const SizedBox(height: 20),
              const Row(
                children: [
                  AppTxt(
                    text: 'Suppliers',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),

              Container(
                padding: const EdgeInsets.symmetric(
                    horizontal: 4.0), // Optional padding
                decoration: const BoxDecoration(
                    // border: Border.all(color: Colors.grey), // Border styling
                    // borderRadius: BorderRadius.circular(5.0), // Rounded corners
                    ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: TypeAheadField<ListSupplier>(
                        getImmediateSuggestions: false,
                        hideOnEmpty: false,
                        minCharsForSuggestions: 1,
                        textFieldConfiguration: TextFieldConfiguration(
                          autofocus: false,
                          controller:
                              _controllersupplier, // Use the same controller
                          decoration: const InputDecoration(
                            suffixIcon: Icon(Icons.arrow_drop_down),
                            hintText: "Select Suppliers",
                            // border: OutlineInputBorder(),
                          ),
                        ),
                        suggestionsCallback: (pattern) async {
                          supplierdetails = await Services()
                              .getSupplierSearchListEntryExit(pattern);
                          return supplierdetails?.list ?? [];
                          // (supplierdetails?.list ?? [])
                          //     .where((supplier) {
                          //   return supplier.supplier!
                          //       .toLowerCase()
                          //       .contains(pattern.toLowerCase());
                          // }).toList();
                        },
                        itemBuilder: (context, ListSupplier suggestion) {
                          return ListTile(
                            title: Text(suggestion.supplier!),
                          );
                        },
                        onSuggestionSelected: (ListSupplier selectedSupplier) {
                          setState(() {
                            this.selectedSupplier =
                                selectedSupplier; // Update selected supplier
                            _controllersupplier.text =
                                selectedSupplier.supplier!; // Update text field
                            supplierName = selectedSupplier
                                .supplierId; // Update supplier ID
                          });
                          print('Selected: ${selectedSupplier.supplier}');
                        },
                      ),
                    ),
                    // const Icon(Icons.arrow_drop_down), // Optional right-aligned icon
                  ],
                ),
              ),

              const SizedBox(height: 10),

              AddSupplier(
                onSupplierAdded: (val) async {
                  context.read<EntryExitCubit>().getSupplierEntry();
                  SupplierSuccess b = val;
                  if (val != null) {
                    setState(() {
                      selectedSupplier = ListSupplier(
                          id: b.id,
                          supplier: '${b.supplier}',
                          supplierId: '${b.supplierId}');
                      _controllersupplier.text = b.supplier.toString();
                    });
                  }
                  showSnackBar('Success');
                },
              ),

              const SizedBox(height: 20),
              const Row(
                children: [
                  AppTxt(
                    text: 'Sub',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              AppTA1(
                controller: _controllersub,
                // key: UniqueKey(),
                hintText: 'Sub',
                labelText: 'Type Sub',
                minLines: 1,
                onTextChange: (val) async {
                  sub = val;
                  //commented
                },
              ),
              const SizedBox(height: 20),
              const Row(
                children: [
                  AppTxt(
                    text: 'Date of Pickup',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              AppTACalendar(
                controller: _controllerpickup,
                // key: UniqueKey(),
                hintText: 'Date',
                labelText: 'Type Sub',
                minLines: 1,
                onTextChange: (val) async {
                  datepickup = val;
                  //commented
                },
              ),
              const SizedBox(height: 20),
              const Row(
                children: [
                  AppTxt(
                    text: 'Load Status',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              DropdownButton<String>(
                value:
                    selectedValueLoadStatus, // Initially set to null (or any initial value)
                hint: const Text(
                    "Select a status"), // Hint text when no value is selected
                items: const [
                  DropdownMenuItem(
                    value: "EMPTY",
                    child: Text("EMPTY"),
                  ),
                  DropdownMenuItem(
                    value: "LOADED",
                    child: Text("LOADED"),
                  ),
                ],
                onChanged: (String? newValue) {
                  setState(() {
                    selectedValueLoadStatus = newValue;
                  });
                },
                // Aligning the dropdown arrow to the rightmost
                icon: const Icon(Icons.arrow_drop_down,
                    color: Colors.black), // Change color as per your theme
                iconEnabledColor: Colors
                    .black, // Optional: Change the color of the arrow if you need
                iconSize: 24.0, // Size of the dropdown icon
                isExpanded:
                    true, // Makes the dropdown take the full width of the parent container
                underline: Container(
                  // Keeps the underline
                  height: .2, // Adjust the thickness of the underline
                  color: Colors.grey, // Color of the underline
                ), // Optional: Removes the underline if you don't need it
              ),
              // AppTA1(
              //   controller: _controllerloadstatus,
              //   // key: UniqueKey(),
              //   hintText: 'Type Load Status',
              //   labelText: 'Type Load Status',
              //   minLines: 1,
              //   onTextChange: (val) async {
              //     loadstatus = val;
              //     //commented
              //   },
              // ),
              const SizedBox(height: 20),
              const AppTxt(
                text: 'Due at Plant',
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              AppTACalendar(
                controller: _controllerdueplant,
                // key: UniqueKey(),
                hintText: 'Due at Plant',
                labelText: '',
                minLines: 1,
                onTextChange: (val) async {
                  setState(() {
                    dueplant = val;
                  }); //commented
                },
              ),
              // AppTA1(
              //   controller: _controllerdueplant,
              //   // key: UniqueKey(),
              //   hintText: 'Type Due at Plant',
              //   labelText: 'Type Due at Plant',
              //   minLines: 1,
              //   onTextChange: (val) async {
              //     dueplant = val;
              //     //commented
              //   },
              // ),
              const SizedBox(height: 20),
              const Row(
                children: [
                  AppTxt(
                    text: 'Sequence Number',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              AppTA1(
                controller: _controllersequence,
                // key: UniqueKey(),
                hintText: 'Type Sequence Number',
                labelText: '',
                minLines: 1,
                onTextChange: (val) async {
                  sequence = val;
                  //commented
                },
              ),
              const SizedBox(height: 20),
              const Row(
                children: [
                  AppTxt(
                    text: 'Date of Arrival',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              AppTACalendar(
                controller: _controllerarrival,
                // key: UniqueKey(),
                hintText: 'Date',
                labelText: '',
                minLines: 1,
                onTextChange: (val) async {
                  arrivaldate = val;
                  //commented
                },
              ),
              const SizedBox(height: 20),
              const Row(
                children: [
                  AppTxt(
                    text: 'Tractor Number',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              AppTA1(
                controller: _controllertractor,
                // key: UniqueKey(),
                hintText: 'Type Tractor Number',
                labelText: '',
                minLines: 1,
                onTextChange: (val) async {
                  tractornumber = val;
                  //commented
                },
              ),
              const SizedBox(height: 20),
              const Row(
                children: [
                  AppTxt(
                    text: 'Updated By',
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  Text(
                    '*', // Asterisk for required
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
              TextField(
                enabled: false,
                controller: updatedby,
                style: const TextStyle(color: Colors.black),
              ),

              const SizedBox(height: 20),
              const AppTxt(
                text: 'Notes',
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              AppTA1(
                controller: _controllernotes,
                // key: UniqueKey(),
                hintText: 'Notes',
                labelText: 'Type Notes',
                minLines: 1,
                onTextChange: (val) async {
                  proNumber = val;
                  //commented
                },
              ),
              const SizedBox(height: 20),

              //commented
              ElevatedButton(
                  onPressed: () {
                    print('Location: $location');

                    print('Carrier------date: ${datepickup.toString()}');

                    print('Carrier: $selectedCarrier');
                    print('sup: ${selectedSupplier?.supplierId}');
                    print(
                        'sup---: ${selectedValueLoadStatus.toString()}-----$selectedValueLoadStatus');
                    //  var fleetId='';

                    // print('Selected Option: $selectedOption');
                    // print('Client ID: $clientId');
                    // print('Client ID: $fleetId');
                    // print('Client ID: $clientId');
                    // print('Location: $location');
                    // print('Spot: $spot');
                    // print('Image: $img');
                    // print('Bill: ${_controllerbill.text}');
                    // print('Carrier Name: ${selectedCarrier?.carrierId}');
                    // print('Supplier Name: ${selectedSupplier?.supplierId}');
                    // print('Sub: $sub');
                    // print('Date Pickup: $datepickup');
                    // print('Load Status: $loadstatus');
                    // print('Sequence: $sequence');
                    // print('Arrival Date: $arrivaldate');
                    // print('Tractor Number: $tractornumber');
                    //                   void printValues() {
                    print('Selected Option: $selectedOption');
                    print('Client ID: $clientId');
                    print('Location: $location');
                    print('Spot: $spot');
                    print('Image: $img');
                    print('Bill: ${_controllerbill.text}');
                    print('Carrier: $selectedCarrier');
                    print('Supplier: ${selectedSupplier?.supplierId}');
                    print('Sub: ${_controllersub.text}');
                    print('Pickup: ${_controllerpickup.text}');
                    print('Load Status: ${_controllerloadstatus.text}');
                    print('Sequence: ${_controllersequence.text}');
                    print('Fleet ID: ${selectedTruck?.fleetId}');
                    print('Arrival: ${_controllerarrival.text}');
                    print('Tractor: ${_controllertractor.text}');
                    print('driver: ${_controlleruser.text}');
// }                    print('Tractor: ${selecteddriver}');

                    // if(clientId==null)return;

                    final valuesToCheck = [
                      selectedOption,
                      clientId,
                      location,
                      spot,
                      // selecteddriver?.userId ??
                      _controlleruser.text,
                      // selectedTruck,
                      // img,
                      // _controllerbill.text,
                      selectedCarrier,
                      selectedSupplier?.supplierId,
                      _controllersub.text,
                      _controllerpickup.text,
                      selectedValueLoadStatus,
                      _controllersequence.text,
                      selectedTruck?.fleetId,
                      _controllerarrival.text,
                      _controllertractor.text,
                    ];

                    if (selectedOption?.isNotEmpty == false ||
                        selectedOption == null) {
                      showSnackBar('Please Select Type');
                    } else if (clientId?.isNotEmpty == false ||
                        clientId == null) {
                      showSnackBar('Please Select Client');
                    } else if (selectedTruck?.fleetId?.isNotEmpty == false ||
                        selectedTruck?.fleetId == null) {
                      showSnackBar('Please Select Trailer');
                    } else if (location?.isNotEmpty == false ||
                        location == null) {
                      showSnackBar('Please Select Location');
                    } else if (spot?.isNotEmpty == false || spot == null) {
                      showSnackBar('Please Select Spot');
                    } else if (_controlleruser.text.isNotEmpty == false) {
                      showSnackBar('Please Select Driver');
                    } else if (selectedCarrier?.isNotEmpty == false ||
                        selectedCarrier == null) {
                      showSnackBar('Please Select Carrier');
                    } else if (selectedSupplier?.supplierId?.isNotEmpty ==
                            false ||
                        selectedSupplier?.supplierId == null) {
                      showSnackBar('Please Select Supplier');
                    } else if (_controllersub.text.isNotEmpty == false) {
                      showSnackBar('Please fill Sub');
                    } else if (_controllerpickup.text.isNotEmpty == false) {
                      showSnackBar('Please Select Date of Pickup');
                    } else if (selectedValueLoadStatus?.toString().isNotEmpty ==
                            false ||
                        selectedValueLoadStatus == null) {
                      showSnackBar('Please Select Load Status');
                    } else if (_controllersequence.text.isNotEmpty == false) {
                      showSnackBar('Please fill Sequence Number');
                    } else if (_controllerarrival.text.isNotEmpty == false) {
                      showSnackBar('Please Select Date of Arrival');
                    } else if (_controllertractor.text.isNotEmpty == false) {
                      showSnackBar('Please fill Tractor Number');
                    } else {
                      print('successsssssss');

                      context.read<EntryExitCubit>().submit(
                            fleetId: '${selectedTruck?.fleetId}',
                            clientId: '$clientId',
                            bill: _controllerbill.text,
                            carrierName: '$selectedCarrier',
                            arrivalDate:
                                convertDate(_controllerarrival.text),
                            datePickup:
                                convertDate(_controllerpickup.text),
                            loadStatus: selectedValueLoadStatus.toString(),
                            proNumber: _controllerpronumber.text,
                            location: '$location',
                            sequence: '$sequence',
                            spot: '$spot',
                            driverId: _controlleruser.text,
                            sub: '$sub',
                            supplierName: '${selectedSupplier?.supplierId}',
                            tractorNumber: _controllertractor.text,
                            selectedOption: '$selectedOption',
                            img: img,
                            notes: _controllernotes.text,
                          );
                    }

                    // if (valuesToCheck
                    //     .every((value) => value?.isNotEmpty == true)) {
                    // context.read<EntryExitCubit>().submit(
                    //       fleetId: '${selectedTruck?.fleetId}',
                    //       clientId: '$clientId',
                    //       bill: '${_controllerbill.text}',
                    //       carrierName: '${selectedCarrier}',
                    //       arrivalDate: '${_controllerarrival.text}',
                    //       datePickup: '${_controllerpickup.text}',
                    //       loadStatus: selectedValueLoadStatus.toString(),
                    //       proNumber: '${_controllerpronumber.text}',
                    //       location: '$location',
                    //       sequence: '$sequence',
                    //       spot: '$spot',
                    //       driverId: '${selecteddriver?.userId}',
                    //       sub: '$sub',
                    //       supplierName: '${selectedSupplier?.supplierId}',
                    //       tractorNumber: '${_controllertractor.text}',
                    //       selectedOption: '$selectedOption',
                    //       img: img,
                    //     );
                    // } else {
                    //   showSnackBar('Please fill required values');
                    // }
                  },
                  child: const Text('Submit')),
              // AppBtn(
              //   loading: createJobState.entryExitLoading,
              //   loadingColor: Colors.white,
              //   bgColor: Theme.of(context).primaryColor,
              //   color: Colors.white,
              //   fontWeight: FontWeight.bold,
              //   text: createJobState.exitSelected ? 'Exit Now' : 'Enter Now',
              //   onPress: () async {
              //     if (null == createJobState.selectedEntryExitLocation) {
              //       showSnackBar('Please Select Location', success: false);
              //       return;
              //     }
              //     if (null == createJobState.selectedTruckDetail) {
              //       if (createJobState.autoCompleteController.text.isNotEmpty) {
              //         showSnackBar(
              //           'Invalid Unit/Trailer #. Please verify and enter the correct trailer/unit #',
              //           success: false,
              //         );
              //         return;
              //       } else {
              //         showSnackBar('Please Select Trailer', success: false);
              //         return;
              //       }
              //     }
              //     if (createJobState.entryExitNotes.trim().isEmpty) {
              //       showSnackBar('Please enter notes', success: false);
              //       return;
              //     }
              //     hideSnackBar();
              //     createJobState.doFleetEntryExit();
              //   },
              // ),
              const SizedBox(height: 40),
              const AppTxt(
                text: 'Entry/Exit Report',
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              const SizedBox(height: 20),

//commented

              // ListView.builder(
              //   shrinkWrap: true,
              //   physics: const NeverScrollableScrollPhysics(),
              //   itemCount: createJobState.entryExitReportListModel.list?.length,
              //   itemBuilder: (context, index) {
              //     ListEntryExitElement entryExit =
              //         createJobState.entryExitReportListModel.list![index];
              //     Color color = entryExit.type?.toLowerCase() == 'entry'
              //         ? Theme.of(context).primaryColorDark.withOpacity(0.6)
              //         : Theme.of(context).primaryColorDark.withOpacity(0.4);
              //     return Container(
              //       padding: const EdgeInsets.all(10),
              //       child: Column(
              //         crossAxisAlignment: CrossAxisAlignment.start,
              //         children: [
              //           Container(
              //             padding: const EdgeInsets.fromLTRB(12, 8, 12, 5),
              //             decoration: BoxDecoration(
              //               color: color,
              //               border: Border.all(color: color),
              //               borderRadius: BorderRadius.circular(20),
              //             ),
              //             child: AppTxt(
              //               text: entryExit.type ?? '',
              //               fontSize: 12,
              //               lineHeight: 1,
              //               color: appWhite,
              //             ),
              //           ),
              //           const SizedBox(height: 5),
              //           AppTxt(text: entryExit.fleet?.remarks ?? ''),
              //           const SizedBox(height: 5),
              //           AppTxt(
              //             text: entryExit.location?.locationName ?? '',
              //             fontWeight: FontWeight.bold,
              //           ),
              //           const SizedBox(height: 5),
              //           AppTxt(
              //             text:
              //                 '${entryExit.location?.street}, ${entryExit.location?.city}, ${entryExit.location?.state}, ${entryExit.location?.country}',
              //           ),
              //           const SizedBox(height: 5),
              //           const AppTxt(text: ''),//note not available in api
              //           const SizedBox(height: 5),
              //           AppTxt(
              //             text: 'Created: ${entryExit.audit!.createdDate}',
              //             fontSize: 14,
              //             color: appGrey,
              //           ),
              //           const SizedBox(height: 3),
              //           AppTxt(
              //             text:
              //                 'Last Modified: ${entryExit.audit!.lastModifiedDate}',
              //             fontSize: 14,
              //             color: appGrey,
              //           ),
              //           const SizedBox(height: 3),
              //           AppTxt(
              //             text:
              //                 'Created By: ${entryExit.audit!.createdBy?.firstName}  ${entryExit.audit!.createdBy?.lastName}',
              //             fontSize: 14,
              //             color: appGrey,
              //           ),
              //           const SizedBox(height: 5),
              //           Divider(color: appGrey),
              //         ],
              //       ),
              //     );
              //   },
              // ),

              //commented
            ],
          ),
        );
      }
    });
  }

  ListLocationElement? getLocationValue(ListLocationElement? location) {
    // print("qqqqq ${supplier?.id}");
    for (var val in locdetails?.list ?? []) {
      if (val.locationId == location?.locationId) {
        return val;
      }
    }
    return null;
  }

  SpotListElement? getSpotValue(SpotListElement? spot) {
    // print("qqqqq ${supplier?.id}");
    for (var val in spotdetails?.list ?? []) {
      if (val.spotId == spot?.spotId) {
        return val;
      }
    }

    return null;
  }

  ListSupplier? getSupplierValue(ListSupplier? supplier) {
    print("qqqqq ${supplier?.id}");
    for (var i in supplierdetails?.list ?? []) {
      if (i.id == supplier?.id) {
        return i;
      }
    }
    return null;
  }

  String? getCarrierValue(String? carrier) {
    print("qqqqq $carrier");
    for (var i in carrierdetails ?? []) {
      if (i.name == carrier) {
        return i;
      }
    }
    return null;
  }

  convertDate(String originalDate) {
    DateTime parsedDate = DateFormat('MM-dd-yyyy').parse(originalDate);

    // Format the DateTime object into the desired format
    String formattedDate = DateFormat('yyyy-MM-dd').format(parsedDate);
    return formattedDate;
  }

  ClientListElement? getClientValue(SpotOnClient? client) {
    print("objectqqqqq ${client?.clientName}");
    print("objectqqqqq ${clientdetails?.list?.length}");
    for (var i in clientdetails?.list ?? []) {
      print('object888---${i.clientName}');
      print('object');
      if (i.clientId == client?.clientId) {
        print('object66t');

        return i;
      }
    }
    return null;
  }

  void clearFields() {
    // Clear all the TextEditingController instances
    _controllerpickup.clear();
    _controllerarrival.clear();
    _controllercarrier.clear();
    _controllerlocation.clear();
    _controllerspot.clear();
    // _controllerclient.clear();
    _controlleruser.clear();
    _controllersupplier.clear();
    _controllertrailer.clear();
    _controllerdueplant.clear();
    _controllersequence.clear();
    _controllerloadstatus.clear();
    _controllersub.clear();
    _controllerpronumber.clear();
    _controllertractor.clear();
    _controllerbill.clear();
    _controllernotes.clear();
    // updatedby.clear();

    // Set all related string variables to null
    carrierName = null;
    // clientId = null;
    supplierName = null;
    selectedOption = null;
    bill = null;
    trailerNuumber = null;
    sub = null;
    proNumber = null;
    dueplant = null;
    datepickup = null;
    loadstatus = null;
    tractornumber = null;
    arrivaldate = null;
    sequence = null;
    spot = null;
    location = null;
    driverId = null;
    img = null;
    fleetId = null;
    // selectedClient = null;
    selecteddriver = null;
    selectedLocation = null;
    selectedOptiondropdown = null;
    selectedSpot = null;
    selectedCarrier = null;
    selectedSupplier = null;
    selectedValueLoadStatus = null;
    // carrierdetails = null;
    // supplierdetails = null;
    // spotdetails = null;
    // locdetails = null;
    // driverdetails = null;
    // clientdetails = null; // Assuming this is also an object or list
  }

  void clearLocationFields() {
    // Clear all the TextEditingController instances

    _controllerlocation.clear();
    _controllerspot.clear();

    // Set all related string variables to null
    // carrierName = null;
    // clientId = null;
    // supplierName = null;
    // selectedOption = null;
    // bill = null;
    // trailerNuumber = null;
    // sub = null;
    // proNumber = null;
    // dueplant = null;
    // datepickup = null;
    // loadstatus = null;
    // tractornumber = null;
    // arrivaldate = null;
    // sequence = null;
    spot = null;
    location = null;
    // driverId = null;
    // img = null;
    // fleetId = null;
    // selectedClient = null;
    // selecteddriver = null;
    selectedLocation = null;
    // selectedOptiondropdown = null;
    selectedSpot = null;
    // selectedCarrier = null;
    // selectedSupplier = null;
    // carrierdetails = null;
    // supplierdetails = null;
    // spotdetails = null;
    // locdetails = null;
    // driverdetails = null;
    // clientdetails = null; // Assuming this is also an object or list
  }

  _entryExitButtons(BuildContext context, CreateJobState jobsState) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.all(
          Radius.circular(40.0),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.3),
            spreadRadius: 3,
            blurRadius: 9,

            // offset: Offset(3, 3),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: AppBtn(
              text: 'Entry',
              borderColor: jobsState.exitSelected
                  ? Colors.transparent
                  : Theme.of(context).primaryColor,
              bgColor: jobsState.exitSelected
                  ? Colors.transparent
                  : Theme.of(context).primaryColor,
              color: jobsState.exitSelected
                  ? Theme.of(context).primaryColor
                  : Colors.white,
              fontWeight: FontWeight.bold,
              onPress: () async {
                jobsState.setExitSelected(false);
                hideSnackBar();
              },
            ),
          ),
          Expanded(
            child: AppBtn(
              text: 'Exit',
              borderColor: !jobsState.exitSelected
                  ? Colors.transparent
                  : Theme.of(context).primaryColor,
              bgColor: !jobsState.exitSelected
                  ? Colors.transparent
                  : Theme.of(context).primaryColor,
              color: !jobsState.exitSelected
                  ? Theme.of(context).primaryColor
                  : Colors.white,
              fontWeight: FontWeight.bold,
              onPress: () async {
                jobsState.setExitSelected(true);
              },
            ),
          )
        ],
      ),
    );
  }
}

class Carrier {
  final String carrierId;
  final String carrier;
  final int id;

  Carrier({
    required this.carrierId,
    required this.carrier,
    required this.id,
  });

  // Factory constructor to create Carrier from JSON
  factory Carrier.fromJson(Map<String, dynamic> json) {
    return Carrier(
      carrierId: json['carrierId'],
      carrier: json['carrier'],
      id: json['id'],
    );
  }
}
