import 'package:spot_on/utils/imports.dart';

class SetNewPwdScreen extends StatelessWidget {
  const SetNewPwdScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    LoginState appState = context.watch<LoginState>();
    return Scaffold(
      backgroundColor: appColor,
      body: GlobalWidget(
        child: AbsorbPointer(
          absorbing: appState.loginLoading,
          child: Container(
            padding: const EdgeInsets.all(0.0),
            color: Colors.white,
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                const Stack(
                  children: [
                    AppLogoHeader(),
                    Padding(
                      padding: EdgeInsets.only(top: 50.0),
                      child: AppCloseBtn(),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const SizedBox(height: 30),
                      AppTF(
                        icon: Icons.lock_outline_rounded,
                        hintText: 'Password',
                        onTextChanged: (val) {
                          appState.p1 = val;
                        },
                      ),
                      const SizedBox(height: 30),
                      AppTF(
                        icon: Icons.lock_outline_rounded,
                        hintText: 'Confirm Password',
                        onTextChanged: (val) {
                          appState.p2 = val;
                        },
                      ),
                      const SizedBox(height: 30),
                      AppTF(
                        icon: Icons.message_outlined,
                        hintText: 'Token',
                        onTextChanged: (val) {
                          appState.emailToken = val;
                        },
                      ),
                      const SizedBox(height: 50),
                      AppBtn(
                        text: 'SUBMIT',
                        onPress: () async {
                          if (appState.p1.isEmpty ||
                              appState.p2.isEmpty ||
                              appState.emailToken.isEmpty) {
                            return false;
                          }
                          if (appState.p1 != appState.p2) {
                            showSnackBar('Passwords are not matching.',
                                success: false);
                            return false;
                          }
                          appState.doResetPassword2();
                        },
                      ),
                      const SizedBox(height: 30.0),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
