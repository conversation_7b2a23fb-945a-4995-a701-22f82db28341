import 'package:flutter/cupertino.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:spot_on/providers/trailer_audit_state.dart';
import 'package:spot_on/utils/imports.dart';
import '../models/spot_on_locations.dart';
import '../models/truck_list_model.dart';
import '../providers/create_job_state.dart';
import '../widgets/add_trailer_popup.dart';
import '../widgets/transit_confirmation_popup.dart';
import 'dart:async';

class TrailerAuditTabletScreen extends StatefulWidget {
  const TrailerAuditTabletScreen({Key? key}) : super(key: key);

  final String screenTitle = "Trailer Audit - Tablet";

  @override
  State<TrailerAuditTabletScreen> createState() =>
      _TrailerAuditTabletScreenState();
}

class _TrailerAuditTabletScreenState extends State<TrailerAuditTabletScreen> {
  var controller = TextEditingController();
  late ScrollController _scrollController;
  
  // Debounce timer for search
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      TrailerAuditState trailerAuditState = context.read<TrailerAuditState>();
      trailerAuditState.clearAll();
      trailerAuditState.getSpotOnLocations();
      trailerAuditState.refresh();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  // Helper method to restore scroll position
  void _restoreScrollPosition(double position) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(position);
      }
    });
  }

  // Method to show add trailer popup
  void _showAddTrailerPopup(BuildContext context,
      TrailerAuditState trailerAuditState, int dockIndex) {
    // Store current scroll position before showing popup
    double currentPosition =
        _scrollController.hasClients ? _scrollController.offset : 0.0;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AddTrailerPopup(
          trailerAuditState: trailerAuditState,
          dockIndex: dockIndex,
          onTrailerAdded: (String newTrailerUnitNumber) async {
            // The addTruckToSpecificDock method now handles:
            // 1. Adding the trailer via API
            // 2. Refreshing the truck list
            // 3. Automatically assigning the trailer to the dock
            // 4. Updating the UI
            // Restore scroll position after successful addition
            _restoreScrollPosition(currentPosition);
          },
        );
      },
    );
  }

  // Method to show transit confirmation popup
  void _showTransitConfirmationPopup(
      BuildContext context, TrailerAuditState trailerAuditState) {
    final pendingValidation = trailerAuditState.pendingTransitValidation;
    if (pendingValidation == null) return;

    final truckDetail = pendingValidation['truckDetail'] as TruckDetail;
    final onCancel = pendingValidation['onCancel'] as VoidCallback;
    final onOverride = pendingValidation['onOverride'] as VoidCallback;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return TransitConfirmationPopup(
          trailerUnitNumber: truckDetail.unitNumber ?? 'Unknown',
          onCancel: () {
            Navigator.of(context).pop();
            trailerAuditState.clearPendingTransitValidation();
            onCancel();
          },
          onOverride: () {
            Navigator.of(context).pop();
            trailerAuditState.clearPendingTransitValidation();
            onOverride();
          },
        );
      },
    );
  }

  // Debounced search method for trailers
  Future<List<TruckDetail>> _searchTrailers(String pattern, TrailerAuditState trailerAuditState) async {
    if (pattern.isEmpty) {
      return [];
    }

    // Use a completer to handle the debounced search
    final Completer<List<TruckDetail>> completer = Completer<List<TruckDetail>>();
    
    // Cancel any existing timer
    _debounceTimer?.cancel();
    
    // Set up new timer with 300ms delay
    _debounceTimer = Timer(const Duration(milliseconds: 300), () async {
      try {
        print('[TrailerAuditTabletScreen] Debounced search for pattern: "$pattern"');
        List<TruckDetail> results = await trailerAuditState.searchTrailers(pattern);
        if (!completer.isCompleted) {
          completer.complete(results);
        }
      } catch (e) {
        print('[TrailerAuditTabletScreen] Search error: $e');
        if (!completer.isCompleted) {
          completer.complete([]);
        }
      }
    });
    
    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    TrailerAuditState trailerAuditState = context.watch<TrailerAuditState>();
    var items = trailerAuditState.trailerAudit?.list ?? [];

    // Check for pending transit validation and show popup
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (trailerAuditState.pendingTransitValidation != null) {
        _showTransitConfirmationPopup(context, trailerAuditState);
      }
    });

    return Scaffold(
      body: SizedBox(
        height: MediaQuery.of(context).size.height,
        child: ModalProgressHUD(
          inAsyncCall: trailerAuditState.isLoading,
          opacity: 0,
          color: Colors.white,
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            color: Colors.white,
            child: Column(
              children: [
                // Location dropdown section - full width at top
                _buildLocationDropdown(trailerAuditState),

                // Tablet layout for dock entries - horizontal scrollable table
                Expanded(
                  child: trailerAuditState.selectedPickUpLocation != null &&
                          trailerAuditState.dockEntries.isNotEmpty
                      ? _buildTabletDockEntriesTable(trailerAuditState)
                      : _buildEmptyStateMessage(trailerAuditState, items),
                ),

                // Save button at bottom
                _buildSaveButton(trailerAuditState),
              ],
            ),
          ),
        ),
      ),
      backgroundColor: Colors.white,
    );
  }

  // Location dropdown widget - reused from mobile version
  Widget _buildLocationDropdown(TrailerAuditState trailerAuditState) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            decoration: const BoxDecoration(
              border: Border(
                bottom: BorderSide(color: Colors.grey, width: 1),
              ),
            ),
            child: DropdownButtonFormField<SpotOnLocation>(
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(vertical: 0),
              ),
              value: trailerAuditState.selectedPickUpLocation,
              icon: trailerAuditState.isLoading
                  ? const CupertinoActivityIndicator()
                  : const Icon(Icons.arrow_drop_down),
              isExpanded: true,
              hint:
                  const Text('Select Location', style: TextStyle(fontSize: 16)),
              onChanged: (SpotOnLocation? spotOnLocation) {
                if (trailerAuditState.isLoading) return;
                trailerAuditState.selectedPickupSpot = null;
                trailerAuditState.selectedPickUpLocation = spotOnLocation;
                trailerAuditState.area = spotOnLocation?.locationName;
                if (spotOnLocation != null) {
                  trailerAuditState.getSpots(
                      locationId: spotOnLocation.locationId!, drop: false);
                }
                trailerAuditState.refresh();
              },
              items: trailerAuditState.spotOnLocationList.list
                  .map<DropdownMenuItem<SpotOnLocation>>(
                      (SpotOnLocation value) {
                return DropdownMenuItem<SpotOnLocation>(
                  value: value,
                  child: Text(value.locationName ?? '',
                      style: const TextStyle(fontSize: 16)),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  // Tablet-specific dock entries table with horizontal layout
  Widget _buildTabletDockEntriesTable(TrailerAuditState trailerAuditState) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Table header row
          _buildTableHeader(),
          const SizedBox(height: 8),

          // Scrollable dock entries
          Expanded(
            child: ListView.separated(
              controller: _scrollController,
              itemCount: trailerAuditState.dockEntries.length,
              separatorBuilder: (context, index) => const Divider(
                height: 16,
                thickness: 1,
                color: Colors.grey,
              ),
              itemBuilder: (context, index) {
                var dockEntry = trailerAuditState.dockEntries[index];
                return _buildTabletDockEntryRow(
                    trailerAuditState, dockEntry, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  // Table header for tablet layout
  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // Dock column header
          Expanded(
            flex: 2,
            child: Text(
              'Dock',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const SizedBox(width: 12),

          // Trailer column header with add button
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Text(
                  'Trailer',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade700,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: () {
                    // This will be handled per row, so we'll show a tooltip or disable here
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text(
                            'Use the + button in each row to add a trailer'),
                        duration: Duration(seconds: 2),
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    child: Icon(
                      Icons.add,
                      color: Theme.of(context).primaryColor,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),

          // Status column header
          Expanded(
            flex: 3,
            child: Text(
              'Status',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const SizedBox(width: 12),

          // Notes column header
          Expanded(
            flex: 4,
            child: Text(
              'Notes',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          const SizedBox(width: 12),

          // Actions column header
          Expanded(
            flex: 2,
            child: Text(
              'Actions',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade700,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  // Single row for tablet dock entry - all elements in horizontal layout
  Widget _buildTabletDockEntryRow(
      TrailerAuditState trailerAuditState, dynamic dockEntry, int index) {
    return Container(
      key: ValueKey('tablet_dock_entry_${index}_${dockEntry.rebuildCounter}'),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      decoration: BoxDecoration(
        // Add green border if dock has been edited
        border: dockEntry.isEdited
            ? Border.all(color: Colors.greenAccent, width: 2)
            : Border.all(color: Colors.grey.shade300, width: 1),
        borderRadius: BorderRadius.circular(8),
        // Use alternating background colors, with green tint if edited
        color: dockEntry.isEdited
            ? Colors.green.shade50
            : (index % 2 == 0 ? Colors.grey.shade200 : Colors.white),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Dock information section
          Expanded(
            flex: 2,
            child: _buildDockInfoSection(dockEntry),
          ),
          const SizedBox(width: 12),

          // Trailer selection section with add button
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Expanded(
                  child: _buildTrailerSelectionSection(
                      trailerAuditState, dockEntry, index),
                ),
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () {
                    trailerAuditState.setDefaultTrailerType();
                    trailerAuditState.loadClients();
                    trailerAuditState.loadCarriers();
                    _showAddTrailerPopup(context, trailerAuditState, index);
                  },
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.add,
                      // color: Theme.of(context).primaryColor,
                      color: Colors.green,
                      size: 16,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),

          // Status selection section
          Expanded(
            flex: 3,
            child: _buildStatusSelectionSection(
                trailerAuditState, dockEntry, index),
          ),
          const SizedBox(width: 12),

          // Notes section
          Expanded(
            flex: 4,
            child: _buildNotesSection(trailerAuditState, dockEntry, index),
          ),
          const SizedBox(width: 12),

          // Action buttons section
          Expanded(
            flex: 2,
            child: _buildActionButtonsSection(trailerAuditState, index),
          ),
        ],
      ),
    );
  }

  // Dock information display
  Widget _buildDockInfoSection(dynamic dockEntry) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey, width: 1),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              dockEntry.spot.spotName ?? '',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          // Show edit indicator if dock has been modified
          if (dockEntry.isEdited)
            const Icon(
              Icons.edit,
              size: 16,
              color: Colors.greenAccent,
            ),
        ],
      ),
    );
  }

  // Trailer selection dropdown
  Widget _buildTrailerSelectionSection(
      TrailerAuditState trailerAuditState, dynamic dockEntry, int index) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey, width: 1),
        ),
      ),
      child: TypeAheadField<TruckDetail>(
        key: ValueKey(
            'tablet_trailer_search_${index}_${dockEntry.selectedTruckDetail?.fleetId ?? 'null'}_${dockEntry.rebuildCounter}'),
        textFieldConfiguration: TextFieldConfiguration(
          controller: dockEntry.trailerController,
          decoration: const InputDecoration(
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 0),
            hintText: 'Trailer',
            hintStyle: TextStyle(fontSize: 14),
          ),
          style: const TextStyle(fontSize: 14),
          onChanged: (text) {
            print('[TrailerAuditTabletScreen] Trailer controller text changed for dock $index: "$text"');
            // Clear selection when user starts typing
            if (dockEntry.selectedTruckDetail != null && 
                text != dockEntry.selectedTruckDetail!.unitNumber) {
              trailerAuditState.updateDockEntry(
                index,
                truckDetail: null,
              );
            }
          },
        ),
                  getImmediateSuggestions: false,
          hideOnEmpty: true,
          hideOnLoading: false,
          minCharsForSuggestions: 1,
          suggestionsCallback: (pattern) async {
            // Only show suggestions when user has typed something
            if (pattern.isEmpty) {
              return [];
            }
            // Use debounced search instead of filtering existing list
            return await _searchTrailers(pattern, trailerAuditState);
          },
        loadingBuilder: (context) {
          return const Padding(
            padding: EdgeInsets.all(8.0),
            child: Center(
              child: SizedBox(
                height: 16,
                width: 16,
                child: CircularProgressIndicator(strokeWidth: 1),
              ),
            ),
          );
        },
        itemBuilder: (context, suggestion) {
          TruckDetail truck = suggestion;
          return Container(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    truck.unitNumber ?? '',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
        onSuggestionSelected: (TruckDetail truck) {
          dockEntry.trailerController.text = truck.unitNumber ?? '';
          trailerAuditState.updateDockEntry(
            index,
            truckDetail: truck,
          );
        },
        noItemsFoundBuilder: (context) {
          return const Padding(
            padding: EdgeInsets.all(12.0),
            child: Text(
              'No trailers found',
              style: TextStyle(color: Colors.grey),
            ),
          );
        },
      ),
    );
  }

  // Status selection dropdown
  Widget _buildStatusSelectionSection(
      TrailerAuditState trailerAuditState, dynamic dockEntry, int index) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey, width: 1),
        ),
      ),
      child: DropdownButtonFormField<FleetStatus>(
        key: ValueKey(
            'tablet_status_dropdown_${index}_${dockEntry.selectedFleetStatus?.id ?? 'null'}_${dockEntry.rebuildCounter}'),
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 0),
        ),
        value: dockEntry.selectedFleetStatus,
        isExpanded: true,
        hint: Text(
          'Select Status',
          style: TextStyle(
            fontSize: 14,
            color: dockEntry.selectedTruckDetail == null
                ? Colors.grey.shade400
                : Colors.grey,
          ),
        ),
        onChanged: dockEntry.selectedTruckDetail == null
            ? null // Disable when no trailer selected
            : (FleetStatus? status) {
                trailerAuditState.updateDockEntry(
                  index,
                  fleetStatus: status,
                );
              },
        items: trailerAuditState.fleetStatuses
            .map<DropdownMenuItem<FleetStatus>>((FleetStatus status) {
          return DropdownMenuItem<FleetStatus>(
            value: status,
            child: Text(status.name, style: const TextStyle(fontSize: 14)),
          );
        }).toList(),
      ),
    );
  }

  // Notes input section
  Widget _buildNotesSection(
      TrailerAuditState trailerAuditState, dynamic dockEntry, int index) {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey, width: 1),
        ),
      ),
      child: TextFormField(
        controller: dockEntry.notesController,
        decoration: const InputDecoration(
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(vertical: 8),
          hintText: 'Enter notes...',
          hintStyle: TextStyle(fontSize: 14),
        ),
        style: const TextStyle(fontSize: 14),
        onChanged: (String value) {
          trailerAuditState.updateDockEntry(
            index,
            notes: value,
          );
        },
      ),
    );
  }

  // Action buttons section (Clear only, Add moved to header)
  Widget _buildActionButtonsSection(
      TrailerAuditState trailerAuditState, int index) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Clear entry button
        GestureDetector(
          onTap: () {
            // Store current scroll position
            double currentPosition =
                _scrollController.hasClients ? _scrollController.offset : 0.0;

            trailerAuditState.clearDockEntry(
              index,
              onSuccess: () => _restoreScrollPosition(currentPosition),
            );
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Icon(
              Icons.close,
              color: Colors.red,
              size: 20,
            ),
          ),
        ),
      ],
    );
  }

  // Empty state message when no location selected or no dock entries
  Widget _buildEmptyStateMessage(
      TrailerAuditState trailerAuditState, List items) {
    if (trailerAuditState.selectedPickUpLocation != null) {
      return const Center(
        child: Text(
          "",
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    } else if (items.isNotEmpty) {
      // Show existing trailer audit items in a tablet-friendly format
      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: items.length,
        itemBuilder: (context, index) {
          var item = items[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Location: ${item.area ?? ''}',
                            style:
                                const TextStyle(fontWeight: FontWeight.bold)),
                        const SizedBox(height: 8),
                        Text('Dock: ${item.slot ?? ''}'),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('Trailer: ${item.trailerNumber ?? ''}'),
                        Text('Status: ${item.trailerStatus ?? ''}'),
                      ],
                    ),
                  ),
                  if (item.notes != null && item.notes!.isNotEmpty)
                    Expanded(
                      child: Text('Notes: ${item.notes}'),
                    ),
                ],
              ),
            ),
          );
        },
      );
    } else {
      return const Center(
        child: Text(
          "Select a location to manage dock entries or view all trailer audits!",
          style: TextStyle(fontSize: 16, color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      );
    }
  }

  // Save button at bottom - only show when location is selected
  Widget _buildSaveButton(TrailerAuditState trailerAuditState) {
    // Return empty widget if no location is selected
    if (trailerAuditState.selectedPickUpLocation == null) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      child: ElevatedButton(
        onPressed: () {
          if (trailerAuditState.dockEntries.isNotEmpty) {
            // Validate dock entries before saving
            List<String> missingFields = [];

            for (int i = 0; i < trailerAuditState.dockEntries.length; i++) {
              var dockEntry = trailerAuditState.dockEntries[i];
              String dockName = dockEntry.spot.spotName ?? 'Dock ${i + 1}';

              // Check only dock entries that have been edited
              if (dockEntry.isEdited) {
                // Only validate trailer+status if the user has started filling them
                // Allow notes-only changes without requiring trailer/status
                bool hasTrailerOrStatus =
                    dockEntry.selectedTruckDetail != null ||
                        dockEntry.selectedFleetStatus != null;

                if (hasTrailerOrStatus) {
                  // If user started adding trailer/status, both are required
                  if (dockEntry.selectedTruckDetail == null) {
                    missingFields.add('$dockName: Trailer is required');
                  }
                  if (dockEntry.selectedFleetStatus == null) {
                    missingFields.add('$dockName: Status is required');
                  }
                }
              }
            }

            if (missingFields.isNotEmpty) {
              // Show validation errors
              String errorMessage =
                  'Missing required fields:\n\n${missingFields.join('\n')}';
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: const Text('Validation Error'),
                    content: Text(errorMessage),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text('OK'),
                      ),
                    ],
                  );
                },
              );
            } else {
              // Store current scroll position before saving
              double currentPosition =
                  _scrollController.hasClients ? _scrollController.offset : 0.0;

              // Save dock entries when all validations pass
              trailerAuditState.saveDockEntries(
                onSuccess: () => _restoreScrollPosition(currentPosition),
              );
            }
          } else {
            showToast("No dock entries to save!");
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.greenAccent,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          'SAVE',
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ),
    );
  }
}
