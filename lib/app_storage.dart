import 'package:flutter/cupertino.dart';
import 'package:image_picker/image_picker.dart';

class AppStorage {
  static List<String> newBolList = [];
  static List<String> completedBolList = [];
  static final ValueNotifier<List<XFile>> imageList =
      ValueNotifier<List<XFile>>([]);
  static bool allowPickup = false;
  static bool allowDropOff = false;
  static bool proceed = false;

  static void clear() {
    newBolList.clear();
    completedBolList.clear();
    imageList.value.clear();
    allowPickup = false;
    allowDropOff = false;
    proceed = false;
  }

  static allowsPickup() {
    allowPickup = true;
  }

  static allowsDropOff() {
    allowDropOff = true;
  }

  static allowToProceed() async {
    await Future.delayed(const Duration(seconds: 2));
    proceed = true;
  }
}
