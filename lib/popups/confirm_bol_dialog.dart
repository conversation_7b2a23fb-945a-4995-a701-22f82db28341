import 'package:flutter/material.dart';
import 'package:spot_on/app_storage.dart';

import '../widgets/bol_upload.dart';

class ConfirmBolDialog extends StatefulWidget {
  const ConfirmBolDialog({
    Key? key,
    required this.isUnsigned,
    required this.jobId,
  }) : super(key: key);

  final bool isUnsigned;
  final String jobId;

  @override
  _ConfirmBolDialogState createState() => _ConfirmBolDialogState();
}

class _ConfirmBolDialogState extends State<ConfirmBolDialog> {
  bool? _selectedOption; // To track the selected radio option

  @override
  void initState() {
    super.initState();
    _selectedOption = true; // Set the initial selected option
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16))),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 24,
        ),
        // Add padding to the whole dialog
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              "Are you sure you have no BoL to upload?",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Spacer(),
                Radio(
                  fillColor: WidgetStateProperty.all(Colors.red),
                  value: true,
                  groupValue: _selectedOption,
                  onChanged: (bool? value) {
                    setState(() {
                      _selectedOption = value;
                    });
                  },
                ),
                const Text("Yes"),
                const Spacer(flex: 2),
                Radio(
                  fillColor: WidgetStateProperty.all(Colors.red),
                  value: false,
                  groupValue: _selectedOption,
                  onChanged: (bool? value) {
                    setState(() {
                      _selectedOption = value;
                    });
                  },
                ),
                const Text('No'),
                const Spacer(),
              ],
            ),
            const SizedBox(height: 16),
            // Add some space between radio buttons and the submit button
            SizedBox(
              width: double.infinity,
              height: 40,
              child: ElevatedButton(
                onPressed: () {
                  if (_selectedOption == true) {
                    widget.isUnsigned
                        ? AppStorage.allowsPickup()
                        : AppStorage.allowsDropOff();
                    Navigator.of(context).pop(); // Close the dialog
                    return;
                  }
                  Navigator.of(context).pop(); // Close the dialog
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BolUpload(
                        isUnsigned: widget.isUnsigned,
                        jobId: widget.jobId,
                      ),
                      // fullscreenDialog: true,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32), // Rounded corners
                  ),
                ),
                child: const Text(
                  'CONTINUE',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
