import 'package:flutter/material.dart';
import 'package:spot_on/app_storage.dart';
import 'package:spot_on/utils/utils.dart';
import 'package:spot_on/widgets/app_txt.dart';
import 'package:spot_on/widgets/trailer_stand_upload.dart';


class ConfirmDocDialog extends StatefulWidget {
  const ConfirmDocDialog({
    Key? key,
    required this.isUnsigned,
    required this.jobId,
  }) : super(key: key);

  final bool isUnsigned;
  final String jobId;

  @override
  _ConfirmDocDialogState createState() => _ConfirmDocDialogState();
}

class _ConfirmDocDialogState extends State<ConfirmDocDialog> {
  bool? _selectedOption; // To track the selected radio option
  final List<String> reasonitems = [
    'No stands available ',
    'All available stands are broken'
  ];
  String? selectedReason;

  @override
  void initState() {
    super.initState();
    _selectedOption = true; // Set the initial selected option
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16))),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 24,
          vertical: 24,
        ),
        // Add padding to the whole dialog
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              "Have you placed a trailer stand under the trailer?",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Spacer(),
                Radio(
                  fillColor: WidgetStateProperty.all(Colors.red),
                  value: true,
                  groupValue: _selectedOption,
                  onChanged: (bool? value) {
                    setState(() {
                      _selectedOption = value;
                    });
                  },
                ),
                const Text("Yes"),
                const Spacer(flex: 2),
                Radio(
                  fillColor: WidgetStateProperty.all(Colors.red),
                  value: false,
                  groupValue: _selectedOption,
                  onChanged: (bool? value) {
                    setState(() {
                      _selectedOption = value;
                    });
                  },
                ),
                const Text('No'),
                const Spacer(flex: 2),
                Radio<bool?>(
                  fillColor: WidgetStateProperty.all(Colors.red),
                  value: null,
                  groupValue: _selectedOption,
                  onChanged: (bool? value) {
                    setState(() {
                      _selectedOption = value;
                    });
                  },
                ),
                const Text('N/A'),
                const Spacer(),
              ],
            ),
            const SizedBox(height: 8),
            Container(
                child: _selectedOption == false
                    ? DropdownButton<String>(
                        hint: const Padding(
                          padding: EdgeInsets.only(
                            left: 16,
                            right: 16,
                          ),
                          child: AppTxt(text: 'Select Reason'),
                        ),
                        value: selectedReason, // Currently selected value
                        icon: const Icon(Icons.arrow_drop_down_outlined),
                        // Icon to show on the dropdown
                        iconSize: 24,
                        elevation: 16,
                        isExpanded: true,
                        style: TextStyle(color: Theme.of(context).primaryColor),
                        onChanged: (String? newValue) {
                          // Update the selected value when an item is selected
                          setState(() {
                            selectedReason = newValue;
                          });
                        },
                        items: reasonitems
                            .map<DropdownMenuItem<String>>((String value) {
                          return DropdownMenuItem<String>(
                            value: value,
                            child: AppTxt(
                              text: value,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          );
                        }).toList(),
                      )
                    : const SizedBox()),
            const SizedBox(height: 16),

            // Add some space between radio buttons and the submit button
            SizedBox(
              width: double.infinity,
              height: 40,
              child: ElevatedButton(
                onPressed: () {
                  print(selectedReason);
                  if (_selectedOption == false && selectedReason == null) {
                    showToast("No reason selected");

                    return;
                  }
                  if (_selectedOption == false && selectedReason != null) {
                    print(selectedReason);
                    Navigator.of(context).pop(); // Close the dialog
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => TrailerStandUpload(
                          reason: selectedReason,
                          jobId: widget.jobId,
                        ),
                        // fullscreenDialog: true,
                      ),
                    );
                  }
                  if (_selectedOption == null) {
                    widget.isUnsigned
                        ? AppStorage.allowsPickup()
                        : AppStorage.allowsDropOff();
                    Navigator.of(context).pop(); // Close the dialog
                    return;
                  }
                  Navigator.of(context).pop(); // Close the dialog
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => TrailerStandUpload(
                        reason: selectedReason,
                        jobId: widget.jobId,
                      ),
                      // fullscreenDialog: true,
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32), // Rounded corners
                  ),
                ),
                child: const Text(
                  'CONTINUE',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
