{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "device_info", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info-2.0.3/", "native_build": true, "dependencies": []}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.9.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_keyboard_visibility", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/", "native_build": true, "dependencies": []}, {"name": "flutter_native_splash", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.0/", "native_build": true, "dependencies": []}, {"name": "fluttertoast", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.6/", "native_build": true, "dependencies": []}, {"name": "image_picker_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12/", "native_build": true, "dependencies": []}, {"name": "location", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location-5.0.3/", "native_build": true, "dependencies": []}, {"name": "modal_progress_hud_nsn", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/modal_progress_hud_nsn-0.4.0/", "native_build": true, "dependencies": []}, {"name": "package_info", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "platform_device_id", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform_device_id-1.0.1/", "native_build": true, "dependencies": ["device_info"]}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.4.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "url_launcher_ios", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": []}, {"name": "webview_flutter_wkwebview", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.13.1/", "native_build": true, "dependencies": []}], "android": [{"name": "device_info", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/device_info-2.0.3/", "native_build": true, "dependencies": []}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.9.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_keyboard_visibility", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility-5.4.1/", "native_build": true, "dependencies": []}, {"name": "flutter_native_splash", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.0/", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.19/", "native_build": true, "dependencies": []}, {"name": "fluttertoast", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.6/", "native_build": true, "dependencies": []}, {"name": "image_picker_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+1/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "location", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location-5.0.3/", "native_build": true, "dependencies": []}, {"name": "modal_progress_hud_nsn", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/modal_progress_hud_nsn-0.4.0/", "native_build": true, "dependencies": []}, {"name": "package_info", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "path_provider_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.4/", "native_build": true, "dependencies": []}, {"name": "platform_device_id", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform_device_id-1.0.1/", "native_build": true, "dependencies": ["device_info"]}, {"name": "shared_preferences_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.2.2/", "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/", "native_build": true, "dependencies": []}, {"name": "url_launcher_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.2/", "native_build": true, "dependencies": []}, {"name": "webview_flutter_android", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-3.16.3/", "native_build": true, "dependencies": []}], "macos": [{"name": "file_selector_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4/", "native_build": true, "dependencies": []}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "firebase_messaging", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-14.9.4/", "native_build": true, "dependencies": ["firebase_core"]}, {"name": "flutter_keyboard_visibility_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_macos-1.0.0/", "native_build": false, "dependencies": []}, {"name": "image_picker_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_macos"]}, {"name": "location", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location-5.0.3/", "native_build": true, "dependencies": []}, {"name": "modal_progress_hud_nsn", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/modal_progress_hud_nsn-0.4.0/", "native_build": true, "dependencies": []}, {"name": "package_info", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "path_provider_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "platform_device_id", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform_device_id-1.0.1/", "native_build": true, "dependencies": ["platform_device_id_macos"]}, {"name": "platform_device_id_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform_device_id_macos-1.0.0/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_foundation", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.4.0/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/", "shared_darwin_source": true, "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.0/", "native_build": true, "dependencies": []}], "linux": [{"name": "file_selector_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.2+1/", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_linux-1.0.0/", "native_build": false, "dependencies": []}, {"name": "image_picker_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_linux"]}, {"name": "modal_progress_hud_nsn", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/modal_progress_hud_nsn-0.4.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "platform_device_id_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform_device_id_linux-1.0.0/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.3.2/", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.1.1/", "native_build": true, "dependencies": []}], "windows": [{"name": "file_selector_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+1/", "native_build": true, "dependencies": []}, {"name": "firebase_core", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core-2.32.0/", "native_build": true, "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_windows-1.0.0/", "native_build": false, "dependencies": []}, {"name": "image_picker_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/", "native_build": false, "dependencies": ["file_selector_windows"]}, {"name": "modal_progress_hud_nsn", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/modal_progress_hud_nsn-0.4.0/", "native_build": true, "dependencies": []}, {"name": "path_provider_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.2.1/", "native_build": false, "dependencies": []}, {"name": "platform_device_id_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform_device_id_windows-1.0.0/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.3.2/", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "url_launcher_windows", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.1/", "native_build": true, "dependencies": []}], "web": [{"name": "firebase_core_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.17.0/", "dependencies": []}, {"name": "firebase_messaging_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.8.7/", "dependencies": ["firebase_core_web"]}, {"name": "flutter_keyboard_visibility_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_web-2.0.0/", "dependencies": []}, {"name": "flutter_native_splash", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.0/", "dependencies": []}, {"name": "fluttertoast", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.6/", "dependencies": []}, {"name": "image_picker_for_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.4/", "dependencies": []}, {"name": "location_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/location_web-4.2.0/", "dependencies": []}, {"name": "modal_progress_hud_nsn", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/modal_progress_hud_nsn-0.4.0/", "dependencies": []}, {"name": "package_info_plus_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_web-2.0.0/", "dependencies": []}, {"name": "platform_device_id_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/platform_device_id_web-1.0.0/", "dependencies": []}, {"name": "shared_preferences_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.3.0/", "dependencies": []}, {"name": "url_launcher_web", "path": "/home/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.1/", "dependencies": []}]}, "dependencyGraph": [{"name": "device_info", "dependencies": []}, {"name": "file_selector_linux", "dependencies": []}, {"name": "file_selector_macos", "dependencies": []}, {"name": "file_selector_windows", "dependencies": []}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_keyboard_visibility", "dependencies": ["flutter_keyboard_visibility_linux", "flutter_keyboard_visibility_macos", "flutter_keyboard_visibility_web", "flutter_keyboard_visibility_windows"]}, {"name": "flutter_keyboard_visibility_linux", "dependencies": []}, {"name": "flutter_keyboard_visibility_macos", "dependencies": []}, {"name": "flutter_keyboard_visibility_web", "dependencies": []}, {"name": "flutter_keyboard_visibility_windows", "dependencies": []}, {"name": "flutter_native_splash", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "fluttertoast", "dependencies": []}, {"name": "image_picker", "dependencies": ["image_picker_android", "image_picker_for_web", "image_picker_ios", "image_picker_linux", "image_picker_macos", "image_picker_windows"]}, {"name": "image_picker_android", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "image_picker_for_web", "dependencies": []}, {"name": "image_picker_ios", "dependencies": []}, {"name": "image_picker_linux", "dependencies": ["file_selector_linux"]}, {"name": "image_picker_macos", "dependencies": ["file_selector_macos"]}, {"name": "image_picker_windows", "dependencies": ["file_selector_windows"]}, {"name": "location", "dependencies": ["location_web"]}, {"name": "location_web", "dependencies": []}, {"name": "modal_progress_hud_nsn", "dependencies": []}, {"name": "package_info", "dependencies": []}, {"name": "package_info_plus_web", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "platform_device_id", "dependencies": ["platform_device_id_macos", "platform_device_id_linux", "platform_device_id_web", "platform_device_id_windows", "device_info"]}, {"name": "platform_device_id_linux", "dependencies": []}, {"name": "platform_device_id_macos", "dependencies": []}, {"name": "platform_device_id_web", "dependencies": []}, {"name": "platform_device_id_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_android", "shared_preferences_foundation", "shared_preferences_linux", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_android", "dependencies": []}, {"name": "shared_preferences_foundation", "dependencies": []}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "webview_flutter", "dependencies": ["webview_flutter_android", "webview_flutter_wkwebview"]}, {"name": "webview_flutter_android", "dependencies": []}, {"name": "webview_flutter_wkwebview", "dependencies": []}], "date_created": "2025-07-30 11:46:15.635443", "version": "3.22.1"}